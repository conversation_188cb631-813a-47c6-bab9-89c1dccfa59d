# Test Email with Attachments using Graph API
# Tests the updated email functionality with CSV attachments

$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
    FromEmail = "<EMAIL>"
    ReportRecipients = @("<EMAIL>","<EMAIL>", "<EMAIL>")
    Debug = $false
}

Write-Host "EMAIL WITH ATTACHMENTS TEST" -ForegroundColor Cyan

# Authentication
Write-Host "Authenticating to Microsoft Graph..." -ForegroundColor Yellow
$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

try {
    $connection = Invoke-RestMethod -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" -Method POST -Body $body
    $token = $connection.access_token
    Write-Host "Authentication successful" -ForegroundColor Green
}
catch {
    Write-Host "Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Graph Request Function
function Invoke-GraphRequest {
    param(
        [string]$Uri,
        [string]$Method = "GET",
        [string]$Body
    )

    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    try {
        if ($Body) {
            return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $headers -Body $Body
        } else {
            return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $headers
        }
    }
    catch {
        Write-Host "Graph API Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Create test CSV files
Write-Host "Creating test CSV files..." -ForegroundColor Yellow

$testData1 = @"
Name,Status,LastActivity
Test App 1,Active,2025-01-01
Test App 2,Inactive,2024-06-01
Test App 3,Disabled,2024-01-01
"@

$testData2 = @"
Name,Reason,Action
Old App 1,No activity for 12 months,Delete
Old App 2,No activity for 18 months,Delete
"@

$testData3 = @"
Name,Reason,Action
Inactive App 1,No activity for 6 months,Disable
Inactive App 2,No activity for 8 months,Disable
"@

$testFile1 = "Test_Full_Report.csv"
$testFile2 = "Test_Deletion_Report.csv"
$testFile3 = "Test_Disabling_Report.csv"

$testData1 | Out-File -FilePath $testFile1 -Encoding UTF8
$testData2 | Out-File -FilePath $testFile2 -Encoding UTF8
$testData3 | Out-File -FilePath $testFile3 -Encoding UTF8

$testReportPaths = @($testFile1, $testFile2, $testFile3)

Write-Host "Created test files: $($testReportPaths -join ', ')" -ForegroundColor Green

# Prepare email with attachments
Write-Host "Preparing email with attachments..." -ForegroundColor Yellow

$subject = "TEST: Enterprise Application Maintenance - Email with Attachments"
$bodyText = @"
This is a TEST EMAIL with CSV attachments from the Enterprise Application Maintenance script.

=== TEST EXECUTION SUMMARY ===
Total Runtime: 5.2 minutes
Script Version: v2.1-enhanced-test
Execution Mode: TEST MODE

=== TEST PROCESSING RESULTS ===
Total Service Principals: 500 (test mode)
Microsoft Apps Skipped: 45
Excluded Types Skipped: 123
Excluded Owners Skipped: 15
Active Applications: 67
Deletion Candidates: 89
Disabling Candidates: 12
Total Errors: 0

=== PERFORMANCE METRICS ===
Hashtable lookups: 234 (fast)
API calls to audit logs: 12 (slow)
Skipped due to filtering: 183 (performance boost)
Hashtable efficiency: 95.1% (higher is better)

=== REPORTS GENERATED ===
The following reports are attached to this email:
$($testReportPaths -join "`n")

This is an automated TEST message from the Enterprise Application Maintenance system.
If you receive this email with attachments, the functionality is working correctly.

Test performed at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@

# Prepare recipients
$toRecipients = @()
foreach ($recipient in $Config.ReportRecipients) {
    $toRecipients += @{
        emailAddress = @{
            address = $recipient
        }
    }
}

# Prepare attachments
$attachments = @()
foreach ($reportPath in $testReportPaths) {
    if (Test-Path $reportPath) {
        try {
            $fileName = Split-Path $reportPath -Leaf
            $fileContent = Get-Content $reportPath -Raw
            $fileBytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)
            $base64Content = [System.Convert]::ToBase64String($fileBytes)
            
            $attachment = @{
                "@odata.type" = "#microsoft.graph.fileAttachment"
                name = $fileName
                contentType = "text/csv"
                contentBytes = $base64Content
            }
            $attachments += $attachment
            Write-Host "Added attachment: $fileName ($([Math]::Round($fileBytes.Length / 1KB, 1)) KB)" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed to attach ${reportPath}: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

# Prepare email message with attachments
$emailMessage = @{
    message = @{
        subject = $subject
        body = @{
            contentType = "Text"
            content = $bodyText
        }
        toRecipients = $toRecipients
        from = @{
            emailAddress = @{
                address = $Config.FromEmail
            }
        }
        attachments = $attachments
    }
    saveToSentItems = $true
}

$emailJson = $emailMessage | ConvertTo-Json -Depth 10

Write-Host "Email prepared with $($attachments.Count) attachments:" -ForegroundColor Cyan
foreach ($attachment in $attachments) {
    Write-Host "  - $($attachment.name)" -ForegroundColor Gray
}

if ($Config.Debug) {
    Write-Host "DEBUG MODE: Saving email to file instead of sending" -ForegroundColor Yellow
    
    $emailPath = "Email_With_Attachments_Debug_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    $emailJson | Out-File -FilePath $emailPath -Encoding UTF8
    Write-Host "Email JSON saved to: $emailPath" -ForegroundColor Green
} else {
    Write-Host "Sending email with attachments via Graph API..." -ForegroundColor Yellow
    
    $uri = "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)/sendMail"
    $response = Invoke-GraphRequest -Uri $uri -Method "POST" -Body $emailJson
    
    if ($null -eq $response) {
        Write-Host "Email with attachments sent successfully!" -ForegroundColor Green
        Write-Host "Recipients: $($Config.ReportRecipients -join ', ')" -ForegroundColor Green
        Write-Host "Attachments: $($attachments | ForEach-Object { $_.name } | Join-String -Separator ', ')" -ForegroundColor Green
    } else {
        Write-Host "Email send failed" -ForegroundColor Red
        Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Yellow
    }
}

# Cleanup test files
Write-Host "Cleaning up test files..." -ForegroundColor Yellow
foreach ($testFile in $testReportPaths) {
    if (Test-Path $testFile) {
        Remove-Item $testFile -Force
        Write-Host "Removed: $testFile" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "EMAIL WITH ATTACHMENTS TEST COMPLETED" -ForegroundColor Magenta
