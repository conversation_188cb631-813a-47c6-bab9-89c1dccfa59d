# ===== GRAPH API EMAIL TEST SCRIPT =====
# Tests email functionality using Microsoft Graph API with same credentials as main script
# Author: AI Assistant
# Version: v1.0
# Date: 2025-01-08

# ===== CONFIGURATION FROM MAIN SCRIPT =====
$Config = @{
    # === AUTHENTICATION SETTINGS (from main script) ===
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"

    # === EMAIL CONFIGURATION (from main script) ===
    FromEmail = "<EMAIL>"
    ReportRecipients = @("<EMAIL>","<EMAIL>", "<EMAIL>")
    
    # === TEST SETTINGS ===
    Debug = $true
}

Write-Host "=== GRAPH API EMAIL TEST ===" -ForegroundColor Cyan
Write-Host "Testing email functionality using Microsoft Graph API" -ForegroundColor Yellow
Write-Host "Using same credentials as Enterprise Application Maintenance script" -ForegroundColor Gray

# ===== AUTHENTICATION =====
Write-Host "Authenticating to Microsoft Graph..." -ForegroundColor Yellow

$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

try {
    $connection = Invoke-RestMethod `
        -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" `
        -Method POST `
        -Body $body

    $token = $connection.access_token
    Write-Host "Successfully authenticated to Microsoft Graph" -ForegroundColor Green
}
catch {
    Write-Host "Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ===== GRAPH REQUEST FUNCTION =====
function Invoke-GraphRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri,
        [string]$Method = "GET",
        [string]$Body,
        [hashtable]$Headers = @{}
    )

    $requestHeaders = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    # Add any additional headers
    foreach ($key in $Headers.Keys) {
        $requestHeaders[$key] = $Headers[$key]
    }

    try {
        if ($Body) {
            return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $requestHeaders -Body $Body
        } else {
            return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $requestHeaders
        }
    }
    catch {
        Write-Host "Graph API Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = [int]$_.Exception.Response.StatusCode
            Write-Host "Status Code: $statusCode" -ForegroundColor Red
        }
        return $null
    }
}

# ===== TEST EMAIL FUNCTION =====
function Send-GraphEmail {
    param(
        [string]$Subject,
        [string]$Body,
        [array]$Recipients,
        [string]$FromEmail
    )

    Write-Host "Preparing email via Microsoft Graph API..." -ForegroundColor Yellow

    # Prepare recipients array
    $toRecipients = @()
    foreach ($recipient in $Recipients) {
        $toRecipients += @{
            emailAddress = @{
                address = $recipient
            }
        }
    }

    # Prepare email message
    $emailMessage = @{
        message = @{
            subject = $Subject
            body = @{
                contentType = "Text"
                content = $Body
            }
            toRecipients = $toRecipients
            from = @{
                emailAddress = @{
                    address = $FromEmail
                }
            }
        }
        saveToSentItems = $true
    }

    $emailJson = $emailMessage | ConvertTo-Json -Depth 10

    Write-Host "Email message prepared:" -ForegroundColor Cyan
    Write-Host "  From: $FromEmail" -ForegroundColor Gray
    Write-Host "  To: $($Recipients -join ', ')" -ForegroundColor Gray
    Write-Host "  Subject: $Subject" -ForegroundColor Gray
    Write-Host "  Body length: $($Body.Length) characters" -ForegroundColor Gray

    if ($Config.Debug) {
        Write-Host "DEBUG MODE: Email prepared but not sent" -ForegroundColor Yellow
        
        # Save email JSON to file for review
        $emailPath = "Graph_Email_Test_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
        $emailJson | Out-File -FilePath $emailPath -Encoding UTF8
        Write-Host "Email JSON saved to: $emailPath" -ForegroundColor Green
        
        # Also save readable version
        $readablePath = "Graph_Email_Test_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
        $readableContent = @"
=== GRAPH API EMAIL TEST ===
From: $FromEmail
To: $($Recipients -join ', ')
Subject: $Subject

Body:
$Body

JSON Payload:
$emailJson
"@
        $readableContent | Out-File -FilePath $readablePath -Encoding UTF8
        Write-Host "Readable email content saved to: $readablePath" -ForegroundColor Green
        
        return $true
    } else {
        # Send email via Graph API
        Write-Host "Sending email via Microsoft Graph API..." -ForegroundColor Yellow
        
        $uri = "https://graph.microsoft.com/v1.0/users/$FromEmail/sendMail"
        $response = Invoke-GraphRequest -Uri $uri -Method "POST" -Body $emailJson
        
        if ($response -ne $null) {
            Write-Host "Email sent successfully via Microsoft Graph API" -ForegroundColor Green
            return $true
        } else {
            Write-Host "Failed to send email via Microsoft Graph API" -ForegroundColor Red
            return $false
        }
    }
}

# ===== TEST GRAPH API PERMISSIONS =====
Write-Host "Testing Graph API permissions..." -ForegroundColor Yellow

# Test 1: Check if we can access the user profile
try {
    $userProfile = Invoke-GraphRequest -Uri "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)"
    if ($userProfile) {
        Write-Host "✓ Can access user profile: $($userProfile.displayName)" -ForegroundColor Green
    } else {
        Write-Host "✗ Cannot access user profile" -ForegroundColor Red
    }
}
catch {
    Write-Host "✗ User profile access failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Check Mail.Send permissions
Write-Host "Checking Mail.Send permissions..." -ForegroundColor Yellow
try {
    # Try to get mailbox settings (requires Mail permissions)
    $mailboxSettings = Invoke-GraphRequest -Uri "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)/mailboxSettings"
    if ($mailboxSettings) {
        Write-Host "✓ Mail permissions appear to be available" -ForegroundColor Green
    } else {
        Write-Host "✗ Mail permissions may not be available" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "✗ Mail permissions test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# ===== RUN EMAIL TEST =====
Write-Host ""
Write-Host "=== RUNNING EMAIL TEST ===" -ForegroundColor Magenta

$testSubject = "TEST: Enterprise Application Maintenance V2 - Graph API Email Test"
$testBody = @"
This is a TEST EMAIL sent via Microsoft Graph API from the Enterprise Application Maintenance V2 script.

=== TEST EXECUTION SUMMARY ===
Total Runtime: 5.2 minutes
Script Version: v2.1-enhanced-test
Execution Mode: TEST MODE

=== TEST PROCESSING RESULTS ===
Total Service Principals: 500 (test mode)
Microsoft Apps Skipped: 45
Excluded Types Skipped: 123
Excluded Owners Skipped: 15 (SharePoint Online filtering)
Active Applications: 67
Deletion Candidates: 89
Disabling Candidates: 12
Total Errors: 0

=== OWNER ANALYSIS TEST ===
SharePoint/Office 365 Owners Found: 15 service principals
Owner-based filtering successfully applied to exclude workflow-related SPs.

=== PERFORMANCE METRICS TEST ===
Hashtable lookups: 234 (fast)
API calls to audit logs: 12 (slow)
Skipped due to filtering: 183 (performance boost)
Hashtable efficiency: 95.1% (higher is better)

=== TEST REPORTS GENERATED ===
Enterprise_Applications_Full_Report_TEST.csv
DeletedApps_Report_TEST.csv
DisabledServicePrincipals_TEST.csv

This is an automated TEST message from the Enterprise Application Maintenance system.
If you receive this email, the Graph API email function is working correctly.

Test performed at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Authentication: Microsoft Graph API with Service Principal
From: $($Config.FromEmail)
"@

$emailResult = Send-GraphEmail -Subject $testSubject -Body $testBody -Recipients $Config.ReportRecipients -FromEmail $Config.FromEmail

Write-Host ""
Write-Host "=== TEST RESULTS ===" -ForegroundColor Magenta

if ($emailResult) {
    Write-Host "✓ Graph API email test completed successfully" -ForegroundColor Green
    if ($Config.Debug) {
        Write-Host "  Email was prepared and saved to files (debug mode)" -ForegroundColor Yellow
        Write-Host "  To actually send, set Debug = false in the script" -ForegroundColor Yellow
    } else {
        Write-Host "  Email was sent via Microsoft Graph API" -ForegroundColor Green
    }
} else {
    Write-Host "✗ Graph API email test failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== RECOMMENDATIONS ===" -ForegroundColor Magenta
Write-Host "1. Verify the service principal has Mail.Send permissions" -ForegroundColor Yellow
Write-Host "2. Check if the from email address is valid in your tenant" -ForegroundColor Yellow
Write-Host "3. Ensure the service principal is granted consent for Mail permissions" -ForegroundColor Yellow
Write-Host "4. Review the saved JSON file to verify the email structure" -ForegroundColor Yellow
Write-Host "5. Set Debug = false to actually send the email" -ForegroundColor Yellow

Write-Host ""
Write-Host "Graph API email test completed" -ForegroundColor Cyan
