# Simple Email Test Script
# Tests email functionality from Enterprise Application Maintenance script

$Config = @{
    FromEmail = "<EMAIL>"
    ReportRecipients = @("<EMAIL>","<EMAIL>", "<EMAIL>")
    SMTPServer = "smtp.office365.com"
    SMTPPort = 587
    UseSSL = $true
    Debug = $true
}

Write-Host "EMAIL FUNCTION TEST" -ForegroundColor Cyan
Write-Host "Testing email functionality..." -ForegroundColor Yellow

function Test-EmailMethods {
    $subject = "TEST: Enterprise Application Maintenance Email Test"
    $body = @"
This is a TEST EMAIL from the Enterprise Application Maintenance script.

Test Results:
- Total Service Principals: 500 (test mode)
- Microsoft Apps Skipped: 45
- Excluded Owners Skipped: 15 (SharePoint Online filtering)
- Active Applications: 67
- Deletion Candidates: 89
- Disabling Candidates: 12

If you receive this email, the email function is working correctly.
Test performed at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@

    Write-Host "Email Configuration:" -ForegroundColor Cyan
    Write-Host "  From: $($Config.FromEmail)" -ForegroundColor Gray
    Write-Host "  To: $($Config.ReportRecipients -join ', ')" -ForegroundColor Gray
    Write-Host "  Subject: $subject" -ForegroundColor Gray

    # Method 1: Check Send-MailMessage
    Write-Host "Method 1: Testing Send-MailMessage..." -ForegroundColor Yellow
    try {
        if (Get-Command Send-MailMessage -ErrorAction SilentlyContinue) {
            Write-Host "  Send-MailMessage available but requires authentication" -ForegroundColor Yellow
        } else {
            Write-Host "  Send-MailMessage not available" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Method 2: Test Outlook COM
    Write-Host "Method 2: Testing Outlook COM Object..." -ForegroundColor Yellow
    try {
        $outlook = New-Object -ComObject Outlook.Application
        $mail = $outlook.CreateItem(0)
        
        $mail.To = $Config.ReportRecipients -join ";"
        $mail.Subject = $subject
        $mail.Body = $body
        
        if ($Config.Debug) {
            Write-Host "  Outlook email prepared (saving as draft)" -ForegroundColor Green
            $mail.Save()
            Write-Host "  Email saved as draft in Outlook" -ForegroundColor Green
            $success = $true
        } else {
            $mail.Send()
            Write-Host "  Email sent via Outlook" -ForegroundColor Green
            $success = $true
        }
        
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
    }
    catch {
        Write-Host "  Outlook method failed: $($_.Exception.Message)" -ForegroundColor Red
        $success = $false
    }

    # Method 3: Save to file
    Write-Host "Method 3: Saving email content to file..." -ForegroundColor Yellow
    try {
        $emailPath = "Email_Test_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
        $body | Out-File -FilePath $emailPath -Encoding UTF8
        Write-Host "  Email content saved to: $emailPath" -ForegroundColor Green
    }
    catch {
        Write-Host "  File save failed: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Diagnostics
    Write-Host "System Diagnostics:" -ForegroundColor Cyan
    Write-Host "  User: $($env:USERNAME)" -ForegroundColor Gray
    Write-Host "  Computer: $($env:COMPUTERNAME)" -ForegroundColor Gray
    Write-Host "  Domain: $($env:USERDOMAIN)" -ForegroundColor Gray
    
    # Test network connectivity
    Write-Host "Network Test:" -ForegroundColor Cyan
    try {
        $testConnection = Test-NetConnection -ComputerName "smtp.office365.com" -Port 587 -WarningAction SilentlyContinue
        if ($testConnection.TcpTestSucceeded) {
            Write-Host "  SMTP connectivity: SUCCESS" -ForegroundColor Green
        } else {
            Write-Host "  SMTP connectivity: FAILED" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "  Network test failed: $($_.Exception.Message)" -ForegroundColor Red
    }

    return $success
}

# Run the test
$result = Test-EmailMethods

Write-Host ""
Write-Host "TEST RESULTS" -ForegroundColor Magenta
if ($result) {
    Write-Host "Email function test: SUCCESS" -ForegroundColor Green
    Write-Host "Check your Outlook drafts folder for the test email" -ForegroundColor Yellow
} else {
    Write-Host "Email function test: FAILED" -ForegroundColor Red
}

Write-Host ""
Write-Host "RECOMMENDATIONS" -ForegroundColor Magenta
Write-Host "1. Check Outlook installation and configuration" -ForegroundColor Yellow
Write-Host "2. Verify network connectivity to SMTP server" -ForegroundColor Yellow
Write-Host "3. Consider using Azure Automation for email sending" -ForegroundColor Yellow
Write-Host "4. Review saved email file for content verification" -ForegroundColor Yellow

Write-Host ""
Write-Host "Email test completed" -ForegroundColor Cyan
