# ===== CONFIGURATION =====
$Config = @{
    # === AUTHENTICATION SETTINGS ===
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "ae2a59d6-d33f-46d6-ab13-527e0d322928"
    ClientSecret = Get-AutomationVariable -Name 'Secret-E2Open-DailyReports'
    
    # === EMAIL CONFIGURATION ===
    FromEmail = "<EMAIL>"
    IAMEmail = "<EMAIL>"
    
    # === SCRIPT SETTINGS ===
    ExpiryWarningDays = 30
    ScriptVersion = "v4.0"
    
    # === OUTPUT CONFIGURATION ===
    SendEmails = $true          # Send email notifications for expiring apps
    ExportExcel = $true         # Export results to Excel file
    ShowConsole = $true         # Display results and summary in console
    Debug = $true              # If true, all emails go to IAMEmail with debug info
}

$IsAzureFunction = $true

function Write-Log {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Message,
        [string]$ForegroundColor, # This parameter is ignored in Azure Function environment but kept for compatibility
        [switch]$NoNewLine
    )

    if ($IsAzureFunction) {
        $timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
        $logMessage = "$timestamp INFO: $Message"
        if ($NoNewLine) {
            Write-Output -NoNewline $logMessage
        } else {
            Write-Output $logMessage
        }
    } else {
        Write-Host -Object $Message -ForegroundColor $ForegroundColor -NoNewline:$NoNewLine
    }
}

# ===== AUTHENTICATION =====
Write-Log "Authenticating to Microsoft Graph..." -ForegroundColor Yellow

# Convert client secret to secure string
$SecureSecret = ConvertTo-SecureString $Config.ClientSecret -AsPlainText -Force
$Credential = New-Object System.Management.Automation.PSCredential($Config.ClientId, $SecureSecret)

# Connect to Microsoft Graph
try {
    Connect-MgGraph -TenantId $Config.TenantId -ClientSecretCredential $Credential -NoWelcome
    Write-Log "✓ Connected to Microsoft Graph" -ForegroundColor Green
} catch {
    Write-Error "Failed to authenticate: $_"
    exit 1
}

# ===== MAIN SCRIPT =====
Write-Log "Fetching application registrations..." -ForegroundColor Yellow

# Get ALL applications with pagination
$allApps = @()
$uri = "https://graph.microsoft.com/v1.0/applications"

do {
    $response = Invoke-MgGraphRequest -Uri $uri -Method GET
    $allApps += $response.value
    $uri = $response.'@odata.nextLink'
} while ($uri)

Write-Log "Processing $($allApps.Count) application registrations..." -ForegroundColor Yellow

$results = @()
$expiringApps = @()

foreach ($app in $allApps) {
    # Get owners
    $owners = try {
        $ownerData = (Invoke-MgGraphRequest -Uri "https://graph.microsoft.com/v1.0/applications/$($app.id)/owners" -Method GET).value
        if ($ownerData -and $ownerData.Count -gt 0) {
            $ownerEmails = $ownerData | Where-Object { $_.mail } | ForEach-Object { $_.mail }
            $ownerNames = $ownerData | Where-Object { $_.displayName } | ForEach-Object { $_.displayName }
            @{
                Names = if ($ownerNames.Count -gt 0) { ($ownerNames -join ", ") } else { "Not Found" }
                Emails = if ($ownerEmails.Count -gt 0) { $ownerEmails } else { @() }
            }
        } else {
            @{ Names = "Not Found"; Emails = @() }
        }
    } catch { 
        @{ Names = "Not Found"; Emails = @() }
    }
    
    # Count active credentials and check for expiring ones
    $now = Get-Date
    $expiryThreshold = $now.AddDays($Config.ExpiryWarningDays)
    $activeSecrets = 0
    $activeCerts = 0
    $expiringSecrets = 0
    $expiringCerts = 0
    $backupCredentials = 0  # Credentials valid beyond 30 days
    $nearestExpiry = $null
    $credentialDetails = @()
    
    # Check secrets
    if ($app.passwordCredentials) {
        $app.passwordCredentials | ForEach-Object {
            $expiryDate = [datetime]$_.endDateTime
            $status = if ($expiryDate -le $now) { "Expired" } elseif ($expiryDate -le $expiryThreshold) { "Expiring" } else { "Active" }
            if ($status -ne "Expired") {
                $activeSecrets++
                if ($status -eq "Expiring") {
                    $expiringSecrets++
                    if (-not $nearestExpiry -or $expiryDate -lt $nearestExpiry) {
                        $nearestExpiry = $expiryDate
                    }
                } else {
                    $backupCredentials++
                }
            }
            $credentialDetails += [PSCustomObject]@{
                Type = "Secret"
                DisplayName = $_.displayName
                KeyId = $_.keyId
                MaskedValue = if ($_.hint) { "$($_.hint)**********" } else { "N/A" }
                ExpiryDate = $expiryDate
                Status = $status
            }
        }
    }
    
    # Check certificates
    if ($app.keyCredentials) {
        $app.keyCredentials | ForEach-Object {
            $expiryDate = [datetime]$_.endDateTime
            $status = if ($expiryDate -le $now) { "Expired" } elseif ($expiryDate -le $expiryThreshold) { "Expiring" } else { "Active" }
            if ($status -ne "Expired") {
                $activeCerts++
                if ($status -eq "Expiring") {
                    $expiringCerts++
                    if (-not $nearestExpiry -or $expiryDate -lt $nearestExpiry) {
                        $nearestExpiry = $expiryDate
                    }
                } else {
                    $backupCredentials++
                }
            }
            $credentialDetails += [PSCustomObject]@{
                Type = "Certificate"
                DisplayName = $_.displayName
                KeyId = $_.keyId
                ExpiryDate = $expiryDate
                Status = $status
            }
        }
    }
    
    $totalActive = $activeSecrets + $activeCerts
    $totalExpiring = $expiringSecrets + $expiringCerts
    
    # Set detailed status with backup credential check
    if ($totalExpiring -gt 0 -and $backupCredentials -gt 0) {
        # Has expiring credentials but also has backup credentials (Partial)
        $details = @()
        if ($activeSecrets -gt 0) { $details += "$activeSecrets secrets" }
        if ($activeCerts -gt 0) { $details += "$activeCerts certs" }
        $details += "($totalExpiring expiring, $backupCredentials backup)"
        $status = "Partial ($($details -join ', '))"

        # Add to expiring apps list for email notifications
        $expiringApps += @{
            AppName = $app.displayName
            AppId = $app.appId
            ObjectId = $app.id
            ExpiryDate = $nearestExpiry
            Notes = $app.notes
            OwnerEmails = $owners.Emails
            OwnerNames = $owners.Names
            CredentialDetails = $credentialDetails
        }
    } elseif ($totalExpiring -gt 0 -and $backupCredentials -eq 0) {
        # Only flag as expiring if there are no backup credentials
        $details = @()
        if ($expiringSecrets -gt 0) { $details += "$expiringSecrets secrets expiring" }
        if ($expiringCerts -gt 0) { $details += "$expiringCerts certs expiring" }
        $status = "Expiring ($($details -join ', '))"
        
        # Add to expiring apps list for email notifications
        $expiringApps += @{
            AppName = $app.displayName
            AppId = $app.appId
            ObjectId = $app.id
            ExpiryDate = $nearestExpiry
            Notes = $app.notes
            OwnerEmails = $owners.Emails
            OwnerNames = $owners.Names
            CredentialDetails = $credentialDetails
        }
    } elseif ($totalActive -eq 0) {
        $status = "Expired"
    } else {
        $details = @()
        if ($activeSecrets -gt 0) { $details += "$activeSecrets secrets" }
        if ($activeCerts -gt 0) { $details += "$activeCerts certs" }
        $status = "Active ($($details -join ', '))"
    }
    
    $results += [PSCustomObject]@{
        AppName = $app.displayName
        AppId = $app.appId
        Status = $status
        Owner = $owners.Names
        Notes = if ([string]::IsNullOrEmpty($app.notes)) { "Empty" } else { $app.notes }
        CreatedDate = if ($app.createdDateTime) { [datetime]$app.createdDateTime } else { "Unknown" }
    }
}

# ===== EMAIL NOTIFICATIONS =====
if ($Config.SendEmails -and $expiringApps.Count -gt 0) {
    Write-Log "`nSending email notifications for $($expiringApps.Count) expiring apps..." -ForegroundColor Yellow
    
    foreach ($expiringApp in $expiringApps) {
        # Determine recipient and BCC
        $originalToEmails = if ($expiringApp.OwnerEmails.Count -gt 0) { 
            $expiringApp.OwnerEmails 
        } else { 
            @($Config.IAMEmail) 
        }
        
        $originalBccRecipients = if ($expiringApp.OwnerEmails.Count -gt 0) {
            @(@{ emailAddress = @{ address = $Config.IAMEmail } })
        } else {
            @() # No BCC if the IAM email is already the main recipient
        }

        $toEmails = $originalToEmails
        $bccRecipients = $originalBccRecipients
        $emailSubject = "Action Required: Entra ID App Registration Credential Expiry"
        $debugInfo = ""

        if ($Config.Debug) {
            $toEmails = @($Config.IAMEmail)
            $bccRecipients = @()
            $emailSubject = "[DEBUG] " + $emailSubject
            $debugInfo = @"
<p><b>--- DEBUG INFORMATION ---</b></p>
<p><b>Original To:</b> $($originalToEmails -join ', ')</p>
<p><b>Original BCC:</b> $($originalBccRecipients.emailAddress.address -join ', ')</p>
<p><b>--- ORIGINAL CONTENT ---</b></p>
"@
        }

        # Build email content
        $noteSection = if (-not [string]::IsNullOrEmpty($expiringApp.Notes)) {
            "<b>Note:</b> $($expiringApp.Notes)<br><br>"
        } else { "" }

        # Build the HTML table for credentials
        $credentialTable = "<table border='1' cellpadding='5' cellspacing='0'><tr><th>Type</th><th>Display Name</th><th>KeyId</th><th>Masked Value</th><th>Expiry Date</th><th>Status</th></tr>"
        foreach ($cred in $expiringApp.CredentialDetails) {
            $statusColor = switch ($cred.Status) {
                "Expired" { "#FFC7CE" } # Light Red
                "Expiring" { "#FFEB9C" } # Light Yellow
                "Active" { "#C6EFCE" } # Light Green
                default { "#FFFFFF" } # White
            }
            $credentialTable += "<tr bgcolor='$statusColor'><td>$($cred.Type)</td><td>$($cred.DisplayName)</td><td>$($cred.KeyId)</td><td>$($cred.MaskedValue)</td><td>$($cred.ExpiryDate.ToString('MM/dd/yyyy HH:mm:ss'))</td><td>$($cred.Status)</td></tr>"
        }
        $credentialTable += "</table>"

        $emailBody = @"
$debugInfo
<p>Hello,</p>

<p>One or more credentials for your Application Registration are expiring soon or have already expired. Please review the details below:</p>

<b>App Reg Name:</b> $($expiringApp.AppName)<br>
<b>Application ID:</b> $($expiringApp.AppId)<br>
<b>Object ID:</b> $($expiringApp.ObjectId)<br>
$noteSection

<p><b>Credential Status:</b></p>
$credentialTable

<p><b>Action Required:</b></p>
<ul>
    <li>If this application is still in use and you are using one of the expiring credentials, please raise a Service Request (SR) to generate a new one.</li>
    <li>If you have already generated a new credential and are actively using it, you can safely ignore this notification for the expiring one.</li>
    <li>If this application is no longer needed, please raise an SR to have it deleted.</li>
</ul>

<p>Link to Service Request form:</p>
<a href="https://jabilit.service-now.com/sp?id=sc_cat_item_guide&sys_id=c3ceec7d873839103d7652873cbb35d4&sysparm_category=ff708a211be3f780a8123377cc4bcbe4">Azure AD App Registration Request</a>

<p><i>***This is an automated mailbox, please DO NOT reply. In case of any queries or issues, please reach <NAME_EMAIL>.</i></p>

<p>Thank You.</p>
"@

        # Send email via Graph API
        $emailPayload = @{
            message = @{
                subject = $emailSubject
                body = @{
                    contentType = "HTML"
                    content = $emailBody
                }
                toRecipients = @(
                    $toEmails | ForEach-Object {
                        @{ emailAddress = @{ address = $_ } }
                    }
                )
                bccRecipients = $bccRecipients
            }
        } | ConvertTo-Json -Depth 10
        
        try {
            Invoke-MgGraphRequest -Uri "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)/sendMail" -Method POST -Body $emailPayload -ContentType "application/json"
            Write-Log "✓ Email sent for: $($expiringApp.AppName)" -ForegroundColor Green
            Write-Log "✓ Owners:"
            $toEmails
        } catch {
            Write-Warning "Failed to send email for $($expiringApp.AppName): $_"
        }
    }
} elseif ($expiringApps.Count -gt 0) {
    Write-Log "`n⚠️  Email notifications disabled - $($expiringApps.Count) apps have expiring credentials" -ForegroundColor Yellow
}

# ===== DISPLAY RESULTS =====
if ($Config.ShowConsole) {
    Write-Log "`n===== APP REGISTRATION REPORT =====" -ForegroundColor Cyan
    $results | Format-Table -AutoSize

    Write-Log "`n===== SUMMARY =====" -ForegroundColor Cyan
    $summary = $results | Group-Object { $_.Status.Split(' ')[0] }
    $summary | ForEach-Object { Write-Log "$($_.Name): $($_.Count)" -ForegroundColor Yellow }
}

<# OLD
# ===== EXPORT TO EXCEL =====
$fileName = "" # Initialize fileName here so it's accessible for summary email
if ($Config.ExportExcel) {
    Write-Log "`nExporting to Excel..." -ForegroundColor Yellow
    $fileName = "AppRegistrations_$(Get-Date -Format 'yyyyMMdd_HHmm').xlsx"

    $results | Export-Excel -Path $fileName `
        -AutoSize `
        -TableStyle Medium2 `
        -WorksheetName "App Registrations" `
        -ConditionalText $(
            New-ConditionalText "Expired" -BackgroundColor LightPink
            New-ConditionalText "Expiring" -BackgroundColor Orange
            New-ConditionalText "Partial" -BackgroundColor "LightSalmon"
            New-ConditionalText "Active" -BackgroundColor LightGreen
            New-ConditionalText "Not Found" -BackgroundColor LightYellow
        ) `
        -FreezeTopRow

    Write-Log "✓ Report exported to: $fileName" -ForegroundColor Green
} #>

# ===== EXPORT TO EXCEL =====
$fileName = $null # Initialize fileName here so it's accessible for summary email
if ($Config.ExportExcel) {
    Write-Log "`nExporting to Excel..." -ForegroundColor Yellow
    
    # Use absolute path and ensure directory exists
    $exportDir = if ($IsAzureFunction -or $env:COMPUTERNAME -like "*HybridWorker*") {
        # For Azure Function or Hybrid Worker, use temp directory
        $env:TEMP
    } else {
        # For local execution, use current directory
        (Get-Location).Path
    }
    
    $fileName = Join-Path $exportDir "AppRegistrations_$(Get-Date -Format 'yyyyMMdd_HHmm').xlsx"
    
    try {
        # Ensure the directory exists
        $parentDir = Split-Path $fileName -Parent
        if (!(Test-Path $parentDir)) {
            New-Item -ItemType Directory -Path $parentDir -Force | Out-Null
        }
        
        $results | Export-Excel -Path $fileName `
            -AutoSize `
            -TableStyle Medium2 `
            -WorksheetName "App Registrations" `
            -ConditionalText $(
                New-ConditionalText "Expired" -BackgroundColor LightPink
                New-ConditionalText "Expiring" -BackgroundColor Orange
                New-ConditionalText "Partial" -BackgroundColor "LightSalmon"
                New-ConditionalText "Active" -BackgroundColor LightGreen
                New-ConditionalText "Not Found" -BackgroundColor LightYellow
            ) `
            -FreezeTopRow

        # Verify the file was created successfully
        if (Test-Path $fileName) {
            $fileInfo = Get-Item $fileName
            Write-Log "✓ Report exported to: $($fileInfo.FullName)" -ForegroundColor Green
            Write-Log "✓ File size: $($fileInfo.Length) bytes" -ForegroundColor Green
        } else {
            Write-Log "❌ Excel file was not created!" -ForegroundColor Red
            $fileName = $null
        }
        
    } catch {
        Write-Log "❌ Excel export failed: $_" -ForegroundColor Red
        Write-Log "❌ Exception Type: $($_.Exception.GetType().Name)" -ForegroundColor Red
        $fileName = $null
    }
}

# ===== SUMMARY REPORT EMAIL =====
Write-Log "`nSending summary report email..." -ForegroundColor Yellow

$totalApps = $allApps.Count
$expiringCount = ($results | Where-Object { $_.Status -like "Expiring*" -or $_.Status -like "Partial*" }).Count
$activeCount = ($results | Where-Object { $_.Status -like "Active*" }).Count
$expiredCount = ($results | Where-Object { $_.Status -eq "Expired" }).Count

$summaryEmailBody = @"
<p>Hello IAM Team,</p>

<p>The Entra ID App Registration Credential Expiry Report has completed.</p>

<p><b>Summary:</b></p>
<ul>
    <li>Total Application Registrations Processed: $totalApps</li>
    <li>Applications with Expiring/Partial Credentials: $expiringCount</li>
    <li>Applications with Active Credentials: $activeCount</li>
    <li>Applications with Expired Credentials: $expiredCount</li>
</ul>

<p>Please find the detailed report attached.</p>

<p><i>***This is an automated mailbox, please DO NOT reply. In case of any queries or issues, please reach <NAME_EMAIL>.</i></p>

<p>Thank You.</p>
"@

$summaryEmailSubject = "Entra ID App Registration Credential Expiry Summary Report"
if ($Config.Debug) {
    $summaryEmailSubject = "[DEBUG] " + $summaryEmailSubject
}

$attachments = @()
if ($Config.ExportExcel -and (Test-Path $fileName)) {
    $attachments += @{
        "@odata.type" = "#microsoft.graph.fileAttachment"
        contentBytes = [System.Convert]::ToBase64String([System.IO.File]::ReadAllBytes($fileName))
        name = (Get-Item $fileName).Name
    }
}

$summaryEmailPayload = @{
    message = @{
        subject = $summaryEmailSubject
        body = @{
            contentType = "HTML"
            content = $summaryEmailBody
        }
        toRecipients = @(
            @{ emailAddress = @{ address = "<EMAIL>" } }
        )
        attachments = $attachments
    }
} | ConvertTo-Json -Depth 10

try {
    Invoke-MgGraphRequest -Uri "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)/sendMail" -Method POST -Body $summaryEmailPayload -ContentType "application/json"
    Write-Log "✓ Summary report email <NAME_EMAIL>" -ForegroundColor Green
} catch {
    Write-Warning "Failed to send summary report email: $_"
}

# ===== COMPLETION STATUS =====
Write-Log "`n===== SCRIPT COMPLETION STATUS =====" -ForegroundColor Cyan
Write-Log "✓ Processed $($allApps.Count) application registrations" -ForegroundColor Green
if ($Config.SendEmails) { 
    Write-Log "✓ Email notifications: $($expiringApps.Count) emails sent" -ForegroundColor Green 
} else {
    Write-Log "○ Email notifications: Disabled" -ForegroundColor Gray
}
if ($Config.ExportExcel) { 
    Write-Log "✓ Excel export: Enabled" -ForegroundColor Green 
} else {
    Write-Log "○ Excel export: Disabled" -ForegroundColor Gray
}
if ($Config.ShowConsole) { 
    Write-Log "✓ Console display: Enabled" -ForegroundColor Green 
} else {
    Write-Log "○ Console display: Disabled" -ForegroundColor Gray
}

# Disconnect from Graph
Disconnect-MgGraph | Out-Null
