﻿# ===== ENTERPRISE APPLICATION MAINTENANCE V2 - ENHANCED VERSION =====
# This version includes Owner-based filtering and optimized sign-in data collection
# Author: AI Assistant
# Version: v2.1-enhanced
# Date: 2025-01-08

# ===== Objectives =====
# - Capture all service principals and associated App Registration if applied
# - Get LastSignIn date or anything that identify inactive SP/App Regs based on InactiveThreshold
# - Capture all required information for further processing based on logic
# - Compile Full Report for review and further processing on the script
# - Logic 1: Exclude Microsoft and other system apps based on publisher, displayName or Type
# - Logic 2: Exclude Service Principals based on Owner property (e.g., SharePoint Online)
# - Logic 3: Delete based on GracePeriodDays tag on the 'Note' field 
# - Logic 4: Disable non active SP/App Registration and tag the 'Note' field
# - Compile Disable/Deletion Reports
# - Send email with necessary information and attachments

# ===== CONFIGURATION =====
$Config = @{
    # === SCRIPT SETTINGS ===
    ScriptVersion = "v2.1-enhanced"
    Debug = $true                       # If true, operations are logged but not executed
    TestMode = $false                    # If true, process only a small subset for testing
    TestModeLimit = 500                  # Number of items to process in test mode

    # === AUTHENTICATION SETTINGS ===
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"

    # === EMAIL CONFIGURATION ===
    FromEmail = "<EMAIL>"
    ReportRecipients = @("<EMAIL>","<EMAIL>", "<EMAIL>")
    MaxAttachmentSizeMB = 25

    # === MAINTENANCE SETTINGS ===
    InactiveThresholdMonths = 12        # Apps inactive for this many months will be disabled
    NewAppGracePeriodDays = 180         # New apps must be this old before being considered for disabling
    DeleteGracePeriodDays = 180         # Recently disabled SPs must wait this long before deletion

    # === MICROSOFT & CUSTOM APP EXCLUSION PATTERNS ===
    MicrosoftTenantId = "f8cdef31-a31e-4b4a-93e4-5f571e91255a"
    ExcludedPublisherPatterns = @(
        "*Microsoft*",
        "*Office*",
        "*Azure*",
        "*Windows*",
        "*Xbox*",
        "*Skype*",
        "*OneDrive*",
        "*SharePoint*",
        "*Teams*",
        "*Outlook*",
        "*Exchange*",
        "*Dynamics*",
        "*Power*",
        "*Visual Studio*"
    )
    ExcludedDisplayNamePatterns = @(
        "*J01*"
    )

    # === SERVICE PRINCIPAL TYPE EXCLUSIONS ===
    ExcludedServicePrincipalTypes = @(
        "ManagedIdentity"               # Azure system identities - should not be managed manually
    )

    # === OWNER-BASED EXCLUSION PATTERNS ===
    ExcludedOwnerPatterns = @(
        "*Office 365 SharePoint Online*",  # SharePoint workflow-related service principals
        # https://learn.microsoft.com/en-us/answers/questions/2115133/app-registered-by-power-virtual-agent-service
        "*Power Virtual Agents Service*", # Reference at top, we need to check this
        "*Azure SQL Managed Instance to Azure AD Resource Provider*", # Need to check this as well
        "*Fabric Identity Management*" # And this
    )

    # === OUTPUT CONFIGURATION ===
    OutputPath = "."                    # Directory for output files
    ExportReports = $true               # Export results to CSV files
    SendEmails = $true                  # Send email notifications - CONFIRMED WORKING
    ShowConsole = $true                 # Display results in console
}

$IsAzureFunction = $false

# ===== GLOBAL VARIABLES =====
$script:fullReport = @()
$script:signInActivities = @{}
$script:servicePrincipalActivities = @{}
$script:signInSummaryData = @{}
$script:processedCount = 0
$script:errorCount = 0
$script:skippedCount = 0
$script:deletionCandidates = @()
$script:disablingCandidates = @()
$script:deletionResults = @()
$script:disabledSPsForReport = @()
$script:errorLog = @()
$script:scriptStartTime = Get-Date
$script:emailSummary = @()

Write-Host "JOB HOST: $($env:COMPUTERNAME)" -ForegroundColor Cyan
Write-Host "Script Version: $($Config.ScriptVersion)" -ForegroundColor Cyan
Write-Host "Debug Mode: $($Config.Debug)" -ForegroundColor Cyan
Write-Host "Test Mode: $($Config.TestMode)" -ForegroundColor Cyan
if ($Config.TestMode) {
    Write-Host "Test Mode Limit: $($Config.TestModeLimit) items per category" -ForegroundColor Yellow
}

# ===== LOGGING FUNCTION =====
function Write-Log {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Message,
        [string]$ForegroundColor,
        [switch]$NoNewLine,
        [switch]$AddToEmailSummary
    )

    $timestamp = Get-Date -Format "HH:mm:ss"
    $elapsedTime = [Math]::Round(((Get-Date) - $script:scriptStartTime).TotalMinutes, 1)
    $logMessage = "[$timestamp | +${elapsedTime}m] $Message"

    if ($IsAzureFunction) {
        $utcTimestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
        $azureLogMessage = "$utcTimestamp INFO: $logMessage"
        if ($NoNewLine) {
            Write-Output -NoNewline $azureLogMessage
        } else {
            Write-Output $azureLogMessage
        }
    } else {
        Write-Host -Object $logMessage -ForegroundColor $ForegroundColor -NoNewline:$NoNewLine
    }

    # Add to email summary if requested
    if ($AddToEmailSummary) {
        $script:emailSummary += $logMessage
    }
}

# ===== AUTHENTICATION =====
Write-Log "Authenticating to Microsoft Graph..." -ForegroundColor Yellow

$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

$connection = Invoke-RestMethod `
    -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" `
    -Method POST `
    -Body $body

$script:token = $connection.access_token
$script:tokenExpiry = (Get-Date).AddMinutes(50)

Write-Log "Connected to Microsoft Graph" -ForegroundColor Green

# ===== HELPER FUNCTIONS =====

function Test-TokenExpiry {
    if ((Get-Date) -gt $script:tokenExpiry) {
        Write-Log "Token expired, refreshing..." -ForegroundColor Yellow

        $body = @{
            Grant_Type    = "client_credentials"
            Scope         = "https://graph.microsoft.com/.default"
            Client_Id     = $Config.ClientId
            Client_Secret = $Config.ClientSecret
        }

        $connection = Invoke-RestMethod `
            -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" `
            -Method POST `
            -Body $body

        $script:token = $connection.access_token
        $script:tokenExpiry = (Get-Date).AddMinutes(50)

        Write-Log "Token refreshed" -ForegroundColor Green
    }
}

function Invoke-GraphRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri,
        [string]$Method = "GET",
        [string]$Body,
        [hashtable]$Headers = @{},
        [int]$MaxRetries = 3
    )

    Test-TokenExpiry

    $requestHeaders = @{
        "Authorization" = "Bearer $script:token"
        "Content-Type" = "application/json"
    }

    # Add any additional headers
    foreach ($key in $Headers.Keys) {
        $requestHeaders[$key] = $Headers[$key]
    }

    $attempt = 0
    while ($attempt -lt $MaxRetries) {
        try {
            if ($Body) {
                return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $requestHeaders -Body $Body
            } else {
                return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $requestHeaders
            }
        }
        catch {
            $attempt++
            $statusCode = $null
            $errorDetails = $null
            $responseHeaders = @{}

            if ($_.Exception.Response) {
                $statusCode = [int]$_.Exception.Response.StatusCode

                # Capture response headers for debugging
                if ($_.Exception.Response.Headers) {
                    foreach ($header in $_.Exception.Response.Headers) {
                        $responseHeaders[$header.Key] = $header.Value -join ','
                    }
                }

                # Try to read error details from response body
                try {
                    $errorStream = $_.Exception.Response.GetResponseStream()
                    if ($errorStream) {
                        $reader = New-Object System.IO.StreamReader($errorStream)
                        $errorDetails = $reader.ReadToEnd()
                        $reader.Close()
                        $errorStream.Close()
                    }
                }
                catch {
                    $errorDetails = "Could not read error response body"
                }
            }

            # Log detailed error information for debugging
            Write-Log "Graph API Error Details:" -ForegroundColor Red
            Write-Log "  URI: $Uri" -ForegroundColor Red
            Write-Log "  Method: $Method" -ForegroundColor Red
            Write-Log "  Status Code: $statusCode" -ForegroundColor Red
            Write-Log "  Exception: $($_.Exception.Message)" -ForegroundColor Red
            if ($errorDetails) {
                Write-Log "  Response Body: $errorDetails" -ForegroundColor Red
            }
            if ($responseHeaders.Count -gt 0) {
                Write-Log "  Response Headers:" -ForegroundColor Red
                foreach ($header in $responseHeaders.GetEnumerator()) {
                    Write-Log "    $($header.Key): $($header.Value)" -ForegroundColor Red
                }
            }

            # Handle rate limiting (429) with exponential backoff
            if ($statusCode -eq 429) {
                $retryAfter = 60  # Default to 60 seconds

                # Try to get Retry-After header
                if ($responseHeaders["Retry-After"]) {
                    $retryAfter = [int]$responseHeaders["Retry-After"]
                }

                if ($attempt -lt $MaxRetries) {
                    $waitTime = [Math]::Min($retryAfter, 300)  # Cap at 5 minutes
                    Write-Log "Rate limited (429). Waiting $waitTime seconds before retry $attempt/$MaxRetries..." -ForegroundColor Yellow
                    Start-Sleep -Seconds $waitTime
                    continue
                }
            }

            # Handle token expiry
            if ($statusCode -eq 401) {
                Write-Log "Token expired during request, refreshing..." -ForegroundColor Yellow
                Test-TokenExpiry
                if ($attempt -lt $MaxRetries) {
                    continue
                }
            }

            # For other errors or max retries reached
            if ($attempt -ge $MaxRetries) {
                Write-Log "Graph API Error after $MaxRetries attempts - FINAL FAILURE" -ForegroundColor Red

                # Save detailed error information to file for analysis
                $errorLogPath = "Graph_API_Error_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
                $errorInfo = @"
Graph API Error Details:
URI: $Uri
Method: $Method
Status Code: $statusCode
Exception: $($_.Exception.Message)
Response Body: $errorDetails
Response Headers: $($responseHeaders | ConvertTo-Json -Depth 2)
Timestamp: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@
                $errorInfo | Out-File -FilePath $errorLogPath -Encoding UTF8
                Write-Log "Detailed error information saved to: $errorLogPath" -ForegroundColor Red

                return $null  # Return null instead of throwing to allow script to continue
            }

            # Wait before retry for other errors
            Start-Sleep -Seconds ([Math]::Pow(2, $attempt))  # Exponential backoff
        }
    }

    return $null
}

function Test-IsMicrosoftApp {
    param([object]$ServicePrincipal)

    # Check if it's from Microsoft tenant
    if ($ServicePrincipal.appOwnerOrganizationId -eq $Config.MicrosoftTenantId) {
        return $true
    }

    # Check publisher name patterns
    if ($ServicePrincipal.publisherName) {
        foreach ($pattern in $Config.ExcludedPublisherPatterns) {
            if ($ServicePrincipal.publisherName -like $pattern) {
                return $true
            }
        }
    }

    # Check display name patterns
    if ($ServicePrincipal.displayName) {
        foreach ($pattern in $Config.ExcludedDisplayNamePatterns) {
            if ($ServicePrincipal.displayName -like $pattern) {
                return $true
            }
        }
    }

    return $false
}

function Test-IsExcludedServicePrincipalType {
    param([object]$ServicePrincipal)

    if ($ServicePrincipal.servicePrincipalType) {
        foreach ($excludedType in $Config.ExcludedServicePrincipalTypes) {
            if ($ServicePrincipal.servicePrincipalType -eq $excludedType) {
                return $true
            }
        }
    }

    return $false
}

function Get-ServicePrincipalOwnerInfo {
    param([object]$ServicePrincipal)

    $ownerInfo = @{
        OwnerNames = @()
        OwnerTypes = @()
        OwnerCount = 0
        OwnerNamesString = "N/A"
        OwnerTypesString = "N/A"
    }

    if ($ServicePrincipal.owners -and $ServicePrincipal.owners.Count -gt 0) {
        foreach ($owner in $ServicePrincipal.owners) {
            try {
                $ownerType = "Unknown"
                $ownerName = "Unknown"

                # Determine owner type and extract name
                if ($owner.'@odata.type' -eq '#microsoft.graph.user') {
                    $ownerType = "User"
                    $ownerName = if ($owner.userPrincipalName) { $owner.userPrincipalName } elseif ($owner.displayName) { $owner.displayName } else { $owner.id }
                }
                elseif ($owner.'@odata.type' -eq '#microsoft.graph.servicePrincipal') {
                    $ownerType = "ServicePrincipal"
                    $ownerName = if ($owner.displayName) { $owner.displayName } elseif ($owner.appId) { $owner.appId } else { $owner.id }
                }
                elseif ($owner.'@odata.type' -eq '#microsoft.graph.group') {
                    $ownerType = "Group"
                    $ownerName = if ($owner.displayName) { $owner.displayName } else { $owner.id }
                }
                elseif ($owner.'@odata.type' -eq '#microsoft.graph.application') {
                    $ownerType = "Application"
                    $ownerName = if ($owner.displayName) { $owner.displayName } elseif ($owner.appId) { $owner.appId } else { $owner.id }
                }
                else {
                    # Fallback for other types
                    $ownerType = if ($owner.'@odata.type') { $owner.'@odata.type'.Replace('#microsoft.graph.', '') } else { "Unknown" }
                    $ownerName = if ($owner.displayName) { $owner.displayName } elseif ($owner.userPrincipalName) { $owner.userPrincipalName } else { $owner.id }
                }

                $ownerInfo.OwnerNames += $ownerName
                $ownerInfo.OwnerTypes += $ownerType
            }
            catch {
                Write-Log "Warning: Could not process owner info for owner ID $($owner.id): $($_.Exception.Message)" -ForegroundColor Yellow
                $ownerInfo.OwnerNames += "Error processing owner"
                $ownerInfo.OwnerTypes += "Error"
            }
        }

        $ownerInfo.OwnerCount = $ownerInfo.OwnerNames.Count
        $ownerInfo.OwnerNamesString = $ownerInfo.OwnerNames -join "; "
        $ownerInfo.OwnerTypesString = $ownerInfo.OwnerTypes -join "; "
    }

    return $ownerInfo
}

function Test-IsExcludedOwner {
    param([object]$ServicePrincipal)

    if (-not $ServicePrincipal.owners -or $ServicePrincipal.owners.Count -eq 0) {
        return $false
    }

    $ownerInfo = Get-ServicePrincipalOwnerInfo -ServicePrincipal $ServicePrincipal

    # Check each owner name against exclusion patterns
    foreach ($ownerName in $ownerInfo.OwnerNames) {
        foreach ($pattern in $Config.ExcludedOwnerPatterns) {
            if ($ownerName -like $pattern) {
                return $true
            }
        }
    }

    return $false
}

function Get-SafeDateString {
    param(
        [string]$DateString,
        [string]$Format = "yyyy-MM-dd HH:mm:ss"
    )

    if (-not $DateString -or $DateString -eq "-" -or $DateString -eq "" -or $null -eq $DateString) {
        return "N/A"
    }

    try {
        $date = [DateTime]::Parse($DateString)
        return $date.ToString($Format)
    }
    catch {
        return $DateString  # Return original if can't parse
    }
}

function Get-DaysSince {
    param([string]$DateString)

    if (-not $DateString -or $DateString -eq "-" -or $DateString -eq "" -or $null -eq $DateString) {
        return "N/A"
    }

    try {
        $date = [DateTime]::Parse($DateString)
        return [Math]::Round(((Get-Date) - $date).TotalDays, 0).ToString()
    }
    catch {
        return "N/A"
    }
}

function Get-ServicePrincipalLastSignIn {
    param(
        [Parameter(Mandatory=$true)]
        [object]$ServicePrincipal,
        [int]$DaysToCheck = 365  # Default to 365 days for 12-month analysis
    )

    try {
        # Step 1: Check servicePrincipalSignInActivities hashtable first (comprehensive historical data)
        if ($script:servicePrincipalActivities.ContainsKey($ServicePrincipal.appId)) {
            $script:hashTableLookupCount++
            $spActivity = $script:servicePrincipalActivities[$ServicePrincipal.appId]

            return @{
                HasSignIn = $true
                LastSignInDateTime = $spActivity.LastSignInDateTime
                Source = "ServicePrincipalActivities"
                AppDisplayName = $spActivity.AppDisplayName
                ServicePrincipalName = $spActivity.ServicePrincipalName
                IsRecentActivity = $false  # Will be determined by date analysis
            }
        }

        # Step 2: Fallback to D30 summary data (faster but limited to 30 days)
        if ($script:signInSummaryData.ContainsKey($ServicePrincipal.appId)) {
            $script:hashTableLookupCount++
            $summaryActivity = $script:signInSummaryData[$ServicePrincipal.appId]

            return @{
                HasSignIn = $true
                LastSignInDateTime = "Recent activity (last 30 days)"
                Source = "D30Summary"
                SuccessfulSignInCount = $summaryActivity.SuccessfulSignInCount
                FailedSignInCount = $summaryActivity.FailedSignInCount
                TotalSignInCount = $summaryActivity.SuccessfulSignInCount + $summaryActivity.FailedSignInCount
                IsRecentActivity = $true
            }
        }

        # Step 3: Final fallback to audit logs for critical missing data (EXPENSIVE API CALL)
        if ($ServicePrincipal.accountEnabled) {
            $script:apiCallCount++
            $cutoffDate = (Get-Date).AddDays(-$DaysToCheck).ToString('yyyy-MM-ddTHH:mm:ssZ')
            $uri = "https://graph.microsoft.com/v1.0/auditLogs/signIns?`$filter=appId eq '$($ServicePrincipal.appId)' and createdDateTime ge $cutoffDate&`$top=1&`$orderby=createdDateTime desc"

            $response = Invoke-GraphRequest -Uri $uri -MaxRetries 2

            if ($response -and $response.value -and $response.value.Count -gt 0) {
                $latestSignIn = $response.value[0]
                return @{
                    HasSignIn = $true
                    LastSignInDateTime = $latestSignIn.createdDateTime
                    Source = "AuditLogs365"
                    UserDisplayName = $latestSignIn.userDisplayName
                    Status = $latestSignIn.status.errorCode
                    IsRecentActivity = $false
                }
            }
        }

        # Step 4: No sign-in activity found anywhere
        return @{
            HasSignIn = $false
            LastSignInDateTime = $null
            Source = "None"
            UserDisplayName = $null
            Status = $null
            IsRecentActivity = $false
        }
    }
    catch {
        return @{
            HasSignIn = $false
            LastSignInDateTime = $null
            Source = "Error"
            UserDisplayName = $null
            Status = $null
            Error = $_.Exception.Message
            IsRecentActivity = $false
        }
    }
}

# ===== REPORT GENERATION FUNCTION =====
function Add-ServicePrincipalToFullReport {
    param(
        [Parameter(Mandatory=$true)]
        [object]$ServicePrincipal,
        [Parameter(Mandatory=$true)]
        [string]$ProcessingStatus,
        [string]$ActionTaken = "None",
        [string]$SkipReason = "",
        [string]$ErrorMessage = "",
        [object]$SignInActivity = $null,
        [object]$AppRegistration = $null,
        [object]$OwnerInfo = $null
    )

    try {
        # Safe string handling for all properties
        $reportEntry = [PSCustomObject]@{
            Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            DisplayName = if ($ServicePrincipal.displayName) { $ServicePrincipal.displayName } else { "N/A" }
            AppId = if ($ServicePrincipal.appId) { $ServicePrincipal.appId } else { "N/A" }
            ObjectId = if ($ServicePrincipal.id) { $ServicePrincipal.id } else { "N/A" }
            ServicePrincipalType = if ($ServicePrincipal.servicePrincipalType) { $ServicePrincipal.servicePrincipalType } else { "N/A" }
            AccountEnabled = if ($null -ne $ServicePrincipal.accountEnabled) { $ServicePrincipal.accountEnabled.ToString() } else { "N/A" }
            CreatedDateTime = Get-SafeDateString -DateString $ServicePrincipal.createdDateTime
            DaysSinceCreated = Get-DaysSince -DateString $ServicePrincipal.createdDateTime
            PublisherName = if ($ServicePrincipal.publisherName) { $ServicePrincipal.publisherName } else { "N/A" }
            AppOwnerOrganizationId = if ($ServicePrincipal.appOwnerOrganizationId) { $ServicePrincipal.appOwnerOrganizationId } else { "N/A" }
            IsMicrosoftApp = (Test-IsMicrosoftApp -ServicePrincipal $ServicePrincipal).ToString()
            OwnerNames = if ($OwnerInfo) { $OwnerInfo.OwnerNamesString } else { "N/A" }
            OwnerTypes = if ($OwnerInfo) { $OwnerInfo.OwnerTypesString } else { "N/A" }
            OwnerCount = if ($OwnerInfo) { $OwnerInfo.OwnerCount.ToString() } else { "0" }
            LastSignInDateTime = if ($SignInActivity -and $SignInActivity.HasSignIn) { Get-SafeDateString -DateString $SignInActivity.LastSignInDateTime } else { "Never" }
            DaysSinceLastSignIn = if ($SignInActivity -and $SignInActivity.HasSignIn) { Get-DaysSince -DateString $SignInActivity.LastSignInDateTime } else { "N/A" }
            SignInSuccessCount = if ($SignInActivity -and $SignInActivity.successfulSignInCount) { $SignInActivity.successfulSignInCount.ToString() } else { "0" }
            SignInFailureCount = if ($SignInActivity -and $SignInActivity.failedSignInCount) { $SignInActivity.failedSignInCount.ToString() } else { "0" }
            HasAppRegistration = if ($AppRegistration) { "Yes" } else { "No" }
            AppRegistrationDisplayName = if ($AppRegistration) { $AppRegistration.displayName } else { "N/A" }
            AppRegistrationId = if ($AppRegistration) { $AppRegistration.id } else { "N/A" }
            ProcessingStatus = $ProcessingStatus
            ActionTaken = $ActionTaken
            SkipReason = $SkipReason
            ErrorMessage = $ErrorMessage
        }

        $script:fullReport += $reportEntry
    }
    catch {
        Write-Log "ERROR in Add-ServicePrincipalToFullReport for $($ServicePrincipal.displayName): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# ===== EMAIL FUNCTION =====
function Send-CompletionEmail {
    param(
        [array]$ReportPaths,
        [array]$EmailSummary
    )

    try {
        $subject = "Enterprise Application Maintenance V2 - Enhanced Completed"
        $totalRunTime = [Math]::Round(((Get-Date) - $script:scriptStartTime).TotalMinutes, 1)

        $body = @"
Enterprise Application Maintenance V2 - Enhanced has completed successfully.

=== EXECUTION SUMMARY ===
Total Runtime: $totalRunTime minutes
Script Version: $($Config.ScriptVersion)
Execution Mode: $(if ($Config.Debug) { 'DEBUG - No changes made' } else { 'PRODUCTION' })

=== PROCESSING RESULTS ===
Total Service Principals: $($script:processedCount)
Microsoft Apps Skipped: $(($script:fullReport | Where-Object { $_.SkipReason -like "*Microsoft*" }).Count)
Excluded Types Skipped: $(($script:fullReport | Where-Object { $_.SkipReason -like "*Excluded Type*" }).Count)
Excluded Owners Skipped: $(($script:fullReport | Where-Object { $_.SkipReason -like "*Excluded Owner*" }).Count)
Active Applications: $(($script:fullReport | Where-Object { $_.ProcessingStatus -eq "Active" }).Count)
Deletion Candidates: $($script:deletionResults.Count)
Disabling Candidates: $($script:disabledSPsForReport.Count)
Total Errors: $($script:errorCount)

=== PERFORMANCE METRICS ===
$($EmailSummary -join "`n")

=== REPORTS GENERATED ===
The following reports are attached to this email:
$($ReportPaths -join "`n")

This is an automated message from the Enterprise Application Maintenance system.
"@

        # Send via Microsoft Graph API (confirmed working)
        Write-Log "Sending email via Microsoft Graph API with $($attachments.Count) attachments..." -ForegroundColor Yellow

        # Prepare recipients for Graph API
        $toRecipients = @()
        foreach ($recipient in $Config.ReportRecipients) {
            $toRecipients += @{
                emailAddress = @{
                    address = $recipient
                }
            }
        }

        # Prepare attachments from report files
        $attachments = @()
        foreach ($reportPath in $ReportPaths) {
            if (Test-Path $reportPath) {
                try {
                    $fileName = Split-Path $reportPath -Leaf
                    $fileContent = Get-Content $reportPath -Raw
                    $fileBytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)
                    $base64Content = [System.Convert]::ToBase64String($fileBytes)

                    $attachment = @{
                        "@odata.type" = "#microsoft.graph.fileAttachment"
                        name = $fileName
                        contentType = "text/csv"
                        contentBytes = $base64Content
                    }
                    $attachments += $attachment
                    Write-Log "Added attachment: $fileName ($([Math]::Round($fileBytes.Length / 1KB, 1)) KB)" -ForegroundColor Green
                }
                catch {
                    Write-Log "Failed to attach ${reportPath}: $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        }

        # Prepare email message for Graph API with attachments
        $emailMessage = @{
            message = @{
                subject = $subject
                body = @{
                    contentType = "Text"
                    content = $body
                }
                toRecipients = $toRecipients
                from = @{
                    emailAddress = @{
                        address = $Config.FromEmail
                    }
                }
                attachments = $attachments
            }
            saveToSentItems = $true
        }

        $emailJson = $emailMessage | ConvertTo-Json -Depth 10

        # Ensure we have a valid token (refresh if needed after long processing)
        $currentTime = Get-Date
        $tokenAge = ($currentTime - $script:scriptStartTime).TotalMinutes
        if ($tokenAge -gt 50) {
            Write-Log "Token may have expired after $([Math]::Round($tokenAge, 1)) minutes, refreshing..." -ForegroundColor Yellow
            try {
                $refreshResult = Get-AccessToken
                if ($refreshResult) {
                    Write-Log "Token refreshed successfully for email sending" -ForegroundColor Green
                } else {
                    Write-Log "Token refresh failed" -ForegroundColor Red
                }
            }
            catch {
                Write-Log "Token refresh error: $($_.Exception.Message)" -ForegroundColor Red
            }
        }

        # Send via Graph API
        $uri = "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)/sendMail"

        Write-Log "Email payload size: $([Math]::Round($emailJson.Length / 1KB, 1)) KB" -ForegroundColor Cyan
        Write-Log "Total attachments size: $([Math]::Round(($attachments | ForEach-Object { [System.Convert]::FromBase64String($_.contentBytes).Length } | Measure-Object -Sum).Sum / 1KB, 1)) KB" -ForegroundColor Cyan

        $response = Invoke-GraphRequest -Uri $uri -Method "POST" -Body $emailJson

        if ($null -eq $response) {
            Write-Log "âœ“ Email sent successfully via Microsoft Graph API with $($attachments.Count) attachments" -ForegroundColor Green -AddToEmailSummary
            Write-Log "âœ“ Recipients: $($Config.ReportRecipients -join ', ')" -ForegroundColor Green
            Write-Log "âœ“ Attachments: $($attachments | ForEach-Object { $_.name } | Join-String -Separator ', ')" -ForegroundColor Green
            return $true
        } else {
            Write-Log "âœ— Email send failed via Graph API" -ForegroundColor Red
            Write-Log "âœ— This indicates a Graph API error occurred - check Graph_API_Error_*.txt file for details" -ForegroundColor Red
            Write-Log "âœ— Common causes: attachment size limits, permission issues, malformed JSON" -ForegroundColor Yellow
        }

        # Fallback: Save email content to file
        Write-Log "Saving email content to file as fallback..." -ForegroundColor Yellow
        $emailPath = "Email_Summary_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
        $body | Out-File -FilePath $emailPath -Encoding UTF8
        Write-Log "Email content saved to: $emailPath" -ForegroundColor Green

        # Also save the Graph API JSON for debugging
        $jsonPath = "Email_GraphAPI_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
        $emailJson | Out-File -FilePath $jsonPath -Encoding UTF8
        Write-Log "Graph API JSON saved to: $jsonPath" -ForegroundColor Green

        return $false
    }
    catch {
        Write-Log "Error in email function: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Log "=== STARTING ENTERPRISE APPLICATION MAINTENANCE V2 - ENHANCED ===" -ForegroundColor Cyan

# ===== GET SIGN-IN ACTIVITIES - OPTIMIZED HASHTABLE APPROACH =====
Write-Log "Getting service principal sign-in activities using optimized hashtable approach..." -ForegroundColor Yellow

# Step 1: PRIMARY - Load servicePrincipalSignInActivities into hashtable (comprehensive historical data)
Write-Log "Loading servicePrincipalSignInActivities into hashtable for fast lookup..." -ForegroundColor Yellow
$spActivityStartTime = Get-Date

try {
    $uri = "https://graph.microsoft.com/beta/reports/servicePrincipalSignInActivities"
    $spActivityCount = 0

    do {
        $response = Invoke-GraphRequest -Uri $uri
        if ($response -and $response.value) {
            foreach ($activity in $response.value) {
                if ($activity.appId) {
                    $script:servicePrincipalActivities[$activity.appId] = @{
                        Source = "ServicePrincipalActivities"
                        AppDisplayName = $activity.appDisplayName
                        ServicePrincipalName = $activity.servicePrincipalName
                        LastSignInDateTime = $activity.lastSignInActivity.lastSignInDateTime
                        DelegatedClientSignInActivity = $activity.delegatedClientSignInActivity
                        ApplicationAuthenticationClientSignInActivity = $activity.applicationAuthenticationClientSignInActivity
                        HasHistoricalActivity = $true
                    }
                    $spActivityCount++
                }
            }
            $uri = $response.'@odata.nextLink'

            # Progress update every 5000 records
            if ($spActivityCount % 5000 -eq 0) {
                Write-Log "Loaded $spActivityCount service principal activities into hashtable..." -ForegroundColor Gray
            }
        } else {
            break
        }
    } while ($uri)

    $spActivityDuration = ((Get-Date) - $spActivityStartTime).TotalSeconds
    Write-Log "Loaded $($script:servicePrincipalActivities.Count) service principal activities in $([Math]::Round($spActivityDuration, 2)) seconds" -ForegroundColor Green
}
catch {
    Write-Log "Warning: Could not load servicePrincipalSignInActivities: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Log "Will rely more heavily on D30 summary and audit logs" -ForegroundColor Yellow
}

# Step 2: FALLBACK - Load D30 summary data for apps not found in primary hashtable
Write-Log "Loading D30 summary data as fallback for missing entries..." -ForegroundColor Yellow
$d30StartTime = Get-Date

try {
    $uri = "https://graph.microsoft.com/beta/reports/getAzureADApplicationSignInSummary(period='D30')"
    $response = Invoke-GraphRequest -Uri $uri

    if ($response -and $response.value) {
        foreach ($activity in $response.value) {
            if ($activity.id) {
                $script:signInSummaryData[$activity.id] = @{
                    Source = "D30Summary"
                    SuccessfulSignInCount = $activity.successfulSignInCount
                    FailedSignInCount = $activity.failedSignInCount
                    AppDisplayName = $activity.appDisplayName
                    HasRecentActivity = $true
                }
            }
        }

        $d30Duration = ((Get-Date) - $d30StartTime).TotalSeconds
        Write-Log "Loaded $($script:signInSummaryData.Count) D30 activities in $([Math]::Round($d30Duration, 2)) seconds" -ForegroundColor Green
    }
}
catch {
    Write-Log "Warning: Could not load D30 summary data: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 3: ANALYSIS - Show coverage and performance
$totalPrimaryData = $script:servicePrincipalActivities.Count
$totalFallbackData = $script:signInSummaryData.Count
$totalCombinedData = $totalPrimaryData + $totalFallbackData

Write-Log "Sign-in data collection summary:" -ForegroundColor Cyan
Write-Log "  Primary (SP Activities): $totalPrimaryData entries" -ForegroundColor Green
Write-Log "  Fallback (D30 Summary): $totalFallbackData entries" -ForegroundColor Yellow
Write-Log "  Total coverage: $totalCombinedData entries" -ForegroundColor Cyan
Write-Log "  Hashtable lookup will be used for fast sign-in data retrieval" -ForegroundColor Green

# ===== GET ALL SERVICE PRINCIPALS WITH OWNER INFORMATION =====
Write-Log "Getting all service principals with owner information for comprehensive analysis..." -ForegroundColor Yellow

$allServicePrincipals = @()
$uri = "https://graph.microsoft.com/v1.0/servicePrincipals?`$expand=owners&`$top=999"

do {
    $response = Invoke-GraphRequest -Uri $uri
    if ($response -and $response.value) {
        $allServicePrincipals += $response.value
        $uri = $response.'@odata.nextLink'

        Write-Log "Retrieved $($allServicePrincipals.Count) service principals so far..." -ForegroundColor Gray
    } else {
        break
    }
} while ($uri)

Write-Log "Found $($allServicePrincipals.Count) total service principals" -ForegroundColor Green

# ===== ANALYZE OWNER PATTERNS IN ALL RESULTS =====
Write-Log "Analyzing owner patterns in all $($allServicePrincipals.Count) service principals..." -ForegroundColor Yellow

$ownerAnalysis = @{}
$analysisLimit = $allServicePrincipals.Count  # Analyze all service principals

for ($i = 0; $i -lt $analysisLimit; $i++) {
    $sp = $allServicePrincipals[$i]

    if ($sp.owners -and $sp.owners.Count -gt 0) {
        $ownerInfo = Get-ServicePrincipalOwnerInfo -ServicePrincipal $sp

        foreach ($ownerName in $ownerInfo.OwnerNames) {
            if ($ownerAnalysis.ContainsKey($ownerName)) {
                $ownerAnalysis[$ownerName]++
            } else {
                $ownerAnalysis[$ownerName] = 1
            }
        }
    }
}

# Display top 20 most common owners
$topOwners = $ownerAnalysis.GetEnumerator() | Sort-Object Value -Descending | Select-Object -First 20
Write-Log "Top 20 most common owners in all $analysisLimit service principals:" -ForegroundColor Cyan
foreach ($owner in $topOwners) {
    Write-Log "  $($owner.Name): $($owner.Value) service principals" -ForegroundColor Gray
}

# Check for SharePoint Online owners specifically
$sharePointOwners = $ownerAnalysis.GetEnumerator() | Where-Object { $_.Name -like "*SharePoint*" -or $_.Name -like "*Office 365*" }
if ($sharePointOwners) {
    Write-Log "Found SharePoint/Office 365 related owners:" -ForegroundColor Yellow
    foreach ($owner in $sharePointOwners) {
        Write-Log "  $($owner.Name): $($owner.Value) service principals" -ForegroundColor Yellow
    }
} else {
    Write-Log "No SharePoint/Office 365 related owners found in all $analysisLimit results" -ForegroundColor Green
}

# ===== PROCESS ALL SERVICE PRINCIPALS =====
Write-Log "Processing $($allServicePrincipals.Count) service principals for comprehensive report..." -ForegroundColor Yellow -AddToEmailSummary

$processLimit = if ($Config.TestMode) { [Math]::Min($Config.TestModeLimit, $allServicePrincipals.Count) } else { $allServicePrincipals.Count }
$processingStartTime = Get-Date

# Performance tracking variables
$apiCallCount = 0
$hashTableLookupCount = 0
$skippedDueToFiltering = 0

for ($i = 0; $i -lt $processLimit; $i++) {
    $sp = $allServicePrincipals[$i]
    $script:processedCount++

    # Show progress every 100 items
    if ($i % 100 -eq 0) {
        Write-Log "Processing $($i + 1)/$processLimit service principals... Current: $($sp.displayName)" -ForegroundColor Cyan
    }

    try {
        # Get owner information first (already expanded in the query)
        $ownerInfo = Get-ServicePrincipalOwnerInfo -ServicePrincipal $sp

        # Determine processing logic first (before expensive API calls)
        $isMicrosoftApp = Test-IsMicrosoftApp -ServicePrincipal $sp
        $isExcludedType = Test-IsExcludedServicePrincipalType -ServicePrincipal $sp
        $isExcludedOwner = Test-IsExcludedOwner -ServicePrincipal $sp

        if ($isMicrosoftApp) {
            # Skip Microsoft applications - no need for sign-in data
            Add-ServicePrincipalToFullReport -ServicePrincipal $sp -ProcessingStatus "Skipped" -SkipReason "Microsoft or Excluded" -SignInActivity $null -OwnerInfo $ownerInfo
            $script:skippedCount++
            $skippedDueToFiltering++
        }
        elseif ($isExcludedType) {
            # Skip excluded service principal types (e.g., ManagedIdentity) - no need for sign-in data
            Add-ServicePrincipalToFullReport -ServicePrincipal $sp -ProcessingStatus "Skipped" -SkipReason "Excluded Type: $($sp.servicePrincipalType)" -SignInActivity $null -OwnerInfo $ownerInfo
            $script:skippedCount++
            $skippedDueToFiltering++
        }
        elseif ($isExcludedOwner) {
            # Skip service principals with excluded owners (e.g., SharePoint Online workflow SPs)
            $excludedOwnerNames = $ownerInfo.OwnerNames | Where-Object {
                $ownerName = $_
                $Config.ExcludedOwnerPatterns | Where-Object { $ownerName -like $_ }
            }
            $skipReason = "Excluded Owner: " + ($excludedOwnerNames -join ', ')
            Add-ServicePrincipalToFullReport -ServicePrincipal $sp -ProcessingStatus "Skipped" -SkipReason $skipReason -SignInActivity $null -OwnerInfo $ownerInfo
            $script:skippedCount++
            $skippedDueToFiltering++
        }
        else {
            # Only get sign-in data for apps that might need it (non-Microsoft, non-excluded)
            $signInActivity = $null
            if ($sp.accountEnabled) {
                # Get sign-in activity for enabled apps (12-month lookback for accurate analysis)
                $signInActivity = Get-ServicePrincipalLastSignIn -ServicePrincipal $sp -DaysToCheck 365
            }

            # Check if it's a deletion candidate (disabled and old)
            if (-not $sp.accountEnabled) {
                # This is a disabled SP - could be deletion candidate
                $script:deletionCandidates += $sp
                Add-ServicePrincipalToFullReport -ServicePrincipal $sp -ProcessingStatus "Deletion Candidate" -ActionTaken "Would Delete" -SignInActivity $signInActivity -OwnerInfo $ownerInfo
            }
            else {
                # Check for inactivity (disabling candidate) - CONSERVATIVE APPROACH
                $shouldDisable = $false
                $inactivityReason = ""
                $processingStatus = "Active"
                $actionTaken = "None"

                # 12-MONTH INACTIVITY ANALYSIS: Use comprehensive data for accurate decisions
                if ($signInActivity -and $signInActivity.HasSignIn) {
                    # App has sign-in activity - determine if it's recent enough
                    if ($signInActivity.Source -eq "D30Summary") {
                        # From D30 summary - definitely active in last 30 days
                        $totalSignIns = $signInActivity.TotalSignInCount
                        if ($totalSignIns -gt 0) {
                            $successCount = $signInActivity.SuccessfulSignInCount
                            $failedCount = $signInActivity.FailedSignInCount
                            $inactivityReason = "Recently active - $successCount successful, $failedCount failed sign-ins in last 30 days"
                        } else {
                            $inactivityReason = "Appeared in D30 activity summary"
                        }
                        # Apps with D30 activity are definitely not candidates for disabling
                    }
                    elseif ($signInActivity.Source -eq "AuditLogs365" -and $signInActivity.LastSignInDateTime) {
                        # From 365-day audit logs - has actual last sign-in date
                        try {
                            $lastSignInDate = [DateTime]::Parse($signInActivity.LastSignInDateTime)
                            $daysSinceLastSignIn = ((Get-Date) - $lastSignInDate).TotalDays
                            $monthsSinceLastSignIn = $daysSinceLastSignIn / 30

                            if ($monthsSinceLastSignIn -gt $Config.InactiveThresholdMonths) {
                                # App hasn't been used for more than 12 months - candidate for disabling
                                $shouldDisable = $true
                                $monthsInactive = [Math]::Round($monthsSinceLastSignIn, 1)
                                $lastSignInFormatted = $lastSignInDate.ToString('yyyy-MM-dd')
                                $inactivityReason = "Inactive for $monthsInactive months (last sign-in: $lastSignInFormatted)"
                                $processingStatus = "Disabling Candidate"
                                $actionTaken = "Would Disable"
                            } else {
                                $daysAgo = [Math]::Round($daysSinceLastSignIn, 0)
                                $inactivityReason = "Active within 12 months (last sign-in: $daysAgo days ago)"
                            }
                        }
                        catch {
                            $inactivityReason = "Sign-in activity found but date parsing failed"
                        }
                    }
                    else {
                        # Has sign-in activity from other sources
                        $inactivityReason = "Sign-in activity detected (source: $($signInActivity.Source))"
                    }
                }
                else {
                    # No sign-in activity found in 12 months - check app age before flagging
                    $appAge = $null
                    if ($sp.createdDateTime) {
                        try {
                            $createdDate = [DateTime]::Parse($sp.createdDateTime)
                            $appAge = ((Get-Date) - $createdDate).TotalDays
                        }
                        catch {
                            # Could not parse creation date
                        }
                    }

                    if ($appAge -and $appAge -lt $Config.NewAppGracePeriodDays) {
                        # App is too new to disable (grace period)
                        $ageInDays = [Math]::Round($appAge, 0)
                        $inactivityReason = "New app - $ageInDays days old - grace period applies"
                    } else {
                        # App is old enough and has no sign-ins in 12 months - strong candidate for disabling
                        $shouldDisable = $true
                        $inactivityReason = "No sign-in activity found in last 12 months"
                        $processingStatus = "Disabling Candidate"
                        $actionTaken = "Would Disable"
                    }
                }

                if ($shouldDisable) {
                    $script:disablingCandidates += $sp
                }

                Add-ServicePrincipalToFullReport -ServicePrincipal $sp -ProcessingStatus $processingStatus -ActionTaken $actionTaken -SkipReason $inactivityReason -SignInActivity $signInActivity -OwnerInfo $ownerInfo
            }
        }
    }
    catch {
        $script:errorCount++
        $errorMessage = $_.Exception.Message
        Write-Log "Error processing SP $($sp.displayName): $errorMessage" -ForegroundColor Red

        # Try to add error entry to report
        try {
            $ownerInfo = Get-ServicePrincipalOwnerInfo -ServicePrincipal $sp
            Add-ServicePrincipalToFullReport -ServicePrincipal $sp -ProcessingStatus "Error" -ErrorMessage $errorMessage -OwnerInfo $ownerInfo
        }
        catch {
            Write-Log "CRITICAL: Could not add error entry to report for $($sp.displayName)" -ForegroundColor Red
        }
    }
}

$processingDuration = ((Get-Date) - $processingStartTime).TotalMinutes
Write-Log "Completed processing $script:processedCount service principals in $([Math]::Round($processingDuration, 1)) minutes" -ForegroundColor Green -AddToEmailSummary
Write-Log "Errors encountered: $script:errorCount" -ForegroundColor $(if ($script:errorCount -gt 0) { "Red" } else { "Green" }) -AddToEmailSummary

# Performance Analysis Summary
Write-Log "Performance Analysis:" -ForegroundColor Cyan -AddToEmailSummary
Write-Log "  Hashtable lookups: $hashTableLookupCount (fast)" -ForegroundColor Green -AddToEmailSummary
Write-Log "  API calls to audit logs: $apiCallCount (slow)" -ForegroundColor Yellow -AddToEmailSummary
Write-Log "  Skipped due to filtering: $skippedDueToFiltering (performance boost)" -ForegroundColor Green -AddToEmailSummary
$efficiencyRatio = if ($script:processedCount -gt 0) { [Math]::Round(($hashTableLookupCount / $script:processedCount) * 100, 1) } else { 0 }
Write-Log "  Hashtable efficiency: $efficiencyRatio% (higher is better)" -ForegroundColor Green -AddToEmailSummary

# Count different skip reasons
$microsoftSkipped = ($script:fullReport | Where-Object { $_.SkipReason -like "*Microsoft*" }).Count
$excludedTypeSkipped = ($script:fullReport | Where-Object { $_.SkipReason -like "*Excluded Type*" }).Count
$excludedOwnerSkipped = ($script:fullReport | Where-Object { $_.SkipReason -like "*Excluded Owner*" }).Count

Write-Log "Microsoft apps skipped: $microsoftSkipped" -ForegroundColor Yellow -AddToEmailSummary
Write-Log "Excluded types skipped: $excludedTypeSkipped" -ForegroundColor Yellow -AddToEmailSummary
Write-Log "Excluded owners skipped: $excludedOwnerSkipped (SharePoint Online filtering)" -ForegroundColor Yellow -AddToEmailSummary
Write-Log "Total skipped: $script:skippedCount" -ForegroundColor Yellow -AddToEmailSummary
Write-Log "Deletion candidates: $($script:deletionCandidates.Count)" -ForegroundColor Red -AddToEmailSummary
Write-Log "Disabling candidates: $($script:disablingCandidates.Count)" -ForegroundColor Yellow -AddToEmailSummary

# ===== EXPORT REPORTS =====
if ($Config.ExportReports) {
    Write-Log "Exporting comprehensive report..." -ForegroundColor Yellow

    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $exportDir = if ($Config.OutputPath) { $Config.OutputPath } else { "." }
    $reportPaths = @()

    # Export full report
    $fullReportPath = Join-Path $exportDir "Enterprise_Applications_Full_Report_$timestamp.csv"
    $script:fullReport | Export-Csv -Path $fullReportPath -NoTypeInformation
    Write-Log "Full report exported to: $fullReportPath" -ForegroundColor Green
    $reportPaths += $fullReportPath

    # Export deletion candidates (from processed report data)
    $deletionCandidateReports = $script:fullReport | Where-Object { $_.ProcessingStatus -eq "Deletion Candidate" }
    if ($deletionCandidateReports.Count -gt 0) {
        $deletionReportPath = Join-Path $exportDir "DeletedApps_Report_$timestamp.csv"
        $deletionCandidateReports | Export-Csv -Path $deletionReportPath -NoTypeInformation
        Write-Log "Deletion candidates exported to: $deletionReportPath - $($deletionCandidateReports.Count) items" -ForegroundColor Green
        $reportPaths += $deletionReportPath

        # Also populate deletion results for email
        $script:deletionResults = $deletionCandidateReports
    }

    # Export disabling candidates (from processed report data)
    $disablingCandidateReports = $script:fullReport | Where-Object { $_.ProcessingStatus -eq "Disabling Candidate" }
    if ($disablingCandidateReports.Count -gt 0) {
        $disablingReportPath = Join-Path $exportDir "DisabledServicePrincipals_$timestamp.csv"
        $disablingCandidateReports | Export-Csv -Path $disablingReportPath -NoTypeInformation
        Write-Log "Disabling candidates exported to: $disablingReportPath - $($disablingCandidateReports.Count) items" -ForegroundColor Green
        $reportPaths += $disablingReportPath

        # Also populate disabled SPs for email
        $script:disabledSPsForReport = $disablingCandidateReports
    }

    # Export error log if any errors occurred
    if ($script:errorCount -gt 0) {
        try {
            $errorReportPath = Join-Path $exportDir "ErrorLog_$timestamp.csv"
            $script:errorLog | Export-Csv -Path $errorReportPath -NoTypeInformation
            Write-Log "Error log exported to: $errorReportPath" -ForegroundColor Yellow
            $reportPaths += $errorReportPath
        }
        catch {
            Write-Log "Error exporting error log: $_" -ForegroundColor Red
        }
    }
} else {
    Write-Log "Report export disabled in configuration" -ForegroundColor Gray
    $reportPaths = @()
}

Write-Log "=== ENTERPRISE APPLICATION MAINTENANCE V2 - ENHANCED COMPLETED ===" -ForegroundColor Cyan

# ===== COMPLETION STATUS =====
Write-Host ""
Write-Log "===== SCRIPT COMPLETION STATUS =====" -ForegroundColor Cyan
Write-Log "Processed deletion candidates: $($script:deletionResults.Count)" -ForegroundColor Green
Write-Log "Processed service principals for disabling: $($script:disabledSPsForReport.Count)" -ForegroundColor Green
Write-Log "Total errors encountered: $($script:errorCount)" -ForegroundColor $(if ($script:errorCount -gt 0) { "Red" } else { "Green" })

# Detailed skip breakdown
$microsoftSkipped = ($script:fullReport | Where-Object { $_.SkipReason -like "*Microsoft*" }).Count
$excludedTypeSkipped = ($script:fullReport | Where-Object { $_.SkipReason -like "*Excluded Type*" }).Count
$excludedOwnerSkipped = ($script:fullReport | Where-Object { $_.SkipReason -like "*Excluded Owner*" }).Count

Write-Log "Microsoft apps skipped: $microsoftSkipped" -ForegroundColor Yellow
Write-Log "Excluded types skipped: $excludedTypeSkipped" -ForegroundColor Yellow
Write-Log "Excluded owners skipped: $excludedOwnerSkipped" -ForegroundColor Yellow
Write-Log "Total skipped: $script:skippedCount" -ForegroundColor Yellow

$activeCount = ($script:fullReport | Where-Object { $_.ProcessingStatus -eq "Active" }).Count
Write-Log "Active applications: $activeCount" -ForegroundColor Green

Write-Log "Script version: $($Config.ScriptVersion)" -ForegroundColor Cyan
Write-Log "Execution mode: $(if ($Config.Debug) { 'DEBUG - No changes made' } else { 'PRODUCTION' })" -ForegroundColor Cyan

if ($Config.ExportReports) {
    Write-Log "Report export: Enabled - $($reportPaths.Count) files" -ForegroundColor Green
} else {
    Write-Log "Report export: Disabled" -ForegroundColor Gray
}

if ($Config.SendEmails) {
    Write-Log "Email notifications: Enabled (Graph API confirmed working)" -ForegroundColor Green
    $emailSent = Send-CompletionEmail -ReportPaths $reportPaths -EmailSummary $script:emailSummary
    if ($emailSent) {
        Write-Log "âœ“ Email notification sent successfully via Microsoft Graph API" -ForegroundColor Green
        Write-Log "âœ“ Recipients: $($Config.ReportRecipients -join ', ')" -ForegroundColor Green
    } else {
        Write-Log "âœ— Email notification failed - check logs above for details" -ForegroundColor Red
    }
} else {
    Write-Log "Email notifications: Disabled" -ForegroundColor Gray
}

if ($Config.TestMode) {
    Write-Host "" -ForegroundColor White
    Write-Log "*** TEST MODE WAS ENABLED - Only processed first $($Config.TestModeLimit) items ***" -ForegroundColor Yellow
    Write-Log "To process all $($allServicePrincipals.Count) service principals, set TestMode = false in configuration" -ForegroundColor Yellow
}

Write-Log "Enterprise Application Maintenance script completed successfully." -ForegroundColor Green

