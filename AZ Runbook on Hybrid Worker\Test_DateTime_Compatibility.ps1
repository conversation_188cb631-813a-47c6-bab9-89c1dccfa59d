# ===== DATETIME COMPATIBILITY TEST =====
# Test if servicePrincipalSignInActivities provides compatible datetime format
# for our existing Get-ServicePrincipalLastSignIn function

$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
}

Write-Host "DATETIME COMPATIBILITY TEST" -ForegroundColor Cyan
Write-Host "Testing servicePrincipalSignInActivities datetime format compatibility" -ForegroundColor Yellow

# Authentication
$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

$connection = Invoke-RestMethod -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" -Method POST -Body $body
$token = $connection.access_token
$headers = @{ "Authorization" = "Bearer $token"; "Content-Type" = "application/json" }

Write-Host "Connected to Microsoft Graph" -ForegroundColor Green

# Helper functions from the main script
function Get-SafeDateString {
    param(
        [string]$DateString,
        [string]$Format = "yyyy-MM-dd HH:mm:ss"
    )
    
    if (-not $DateString -or $DateString -eq "-" -or $DateString -eq "" -or $DateString -eq $null) {
        return "N/A"
    }
    
    try {
        $date = [DateTime]::Parse($DateString)
        return $date.ToString($Format)
    }
    catch {
        return $DateString  # Return original if can't parse
    }
}

function Get-DaysSince {
    param([string]$DateString)
    
    if (-not $DateString -or $DateString -eq "-" -or $DateString -eq "" -or $DateString -eq $null) {
        return "N/A"
    }
    
    try {
        $date = [DateTime]::Parse($DateString)
        return [Math]::Round(((Get-Date) - $date).TotalDays, 0).ToString()
    }
    catch {
        return "N/A"
    }
}

# Test servicePrincipalSignInActivities
Write-Host "Getting servicePrincipalSignInActivities sample data..." -ForegroundColor Yellow

try {
    $uri = "https://graph.microsoft.com/beta/reports/servicePrincipalSignInActivities?`$top=50"
    $response = Invoke-RestMethod -Uri $uri -Headers $headers
    
    if ($response.value -and $response.value.Count -gt 0) {
        Write-Host "Found $($response.value.Count) service principal activities" -ForegroundColor Green
        
        # Test datetime parsing for first 10 entries
        $testResults = @()
        $successCount = 0
        $errorCount = 0
        
        for ($i = 0; $i -lt [Math]::Min(10, $response.value.Count); $i++) {
            $activity = $response.value[$i]
            
            $testResult = [PSCustomObject]@{
                AppId = $activity.appId
                AppDisplayName = $activity.appDisplayName
                ServicePrincipalName = $activity.servicePrincipalName
                RawLastSignInDateTime = $activity.lastSignInActivity.lastSignInDateTime
                ParsedDateTime = "N/A"
                FormattedDateTime = "N/A"
                DaysSince = "N/A"
                ParseSuccess = $false
                ErrorMessage = ""
            }
            
            try {
                if ($activity.lastSignInActivity.lastSignInDateTime) {
                    # Test our existing parsing functions
                    $testResult.ParsedDateTime = $activity.lastSignInActivity.lastSignInDateTime
                    $testResult.FormattedDateTime = Get-SafeDateString -DateString $activity.lastSignInActivity.lastSignInDateTime
                    $testResult.DaysSince = Get-DaysSince -DateString $activity.lastSignInActivity.lastSignInDateTime
                    $testResult.ParseSuccess = $true
                    $successCount++
                } else {
                    $testResult.ErrorMessage = "No lastSignInDateTime available"
                    $errorCount++
                }
            }
            catch {
                $testResult.ErrorMessage = $_.Exception.Message
                $testResult.ParseSuccess = $false
                $errorCount++
            }
            
            $testResults += $testResult
        }
        
        # Display results
        Write-Host ""
        Write-Host "DATETIME PARSING TEST RESULTS" -ForegroundColor Magenta
        Write-Host "Successful parses: $successCount" -ForegroundColor Green
        Write-Host "Failed parses: $errorCount" -ForegroundColor Red
        
        Write-Host ""
        Write-Host "Sample Results:" -ForegroundColor Yellow
        foreach ($result in $testResults) {
            $status = if ($result.ParseSuccess) { "SUCCESS" } else { "FAILED" }
            $color = if ($result.ParseSuccess) { "Green" } else { "Red" }
            
            Write-Host "  App: $($result.AppDisplayName)" -ForegroundColor Gray
            Write-Host "    Status: $status" -ForegroundColor $color
            Write-Host "    Raw DateTime: $($result.RawLastSignInDateTime)" -ForegroundColor Gray
            Write-Host "    Formatted: $($result.FormattedDateTime)" -ForegroundColor Gray
            Write-Host "    Days Since: $($result.DaysSince)" -ForegroundColor Gray
            if ($result.ErrorMessage) {
                Write-Host "    Error: $($result.ErrorMessage)" -ForegroundColor Red
            }
            Write-Host ""
        }
        
        # Test compatibility with existing logic
        Write-Host "COMPATIBILITY TEST" -ForegroundColor Magenta
        
        if ($successCount -gt 0) {
            Write-Host "✓ servicePrincipalSignInActivities datetime format is COMPATIBLE" -ForegroundColor Green
            Write-Host "✓ Existing Get-SafeDateString function works correctly" -ForegroundColor Green
            Write-Host "✓ Existing Get-DaysSince function works correctly" -ForegroundColor Green
            Write-Host "✓ Safe to implement hashtable fallback strategy" -ForegroundColor Green
        } else {
            Write-Host "✗ DateTime parsing failed - need to investigate format" -ForegroundColor Red
        }
        
        # Export detailed results
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $exportPath = "DateTime_Compatibility_Test_$timestamp.csv"
        $testResults | Export-Csv -Path $exportPath -NoTypeInformation
        Write-Host "Detailed results exported to: $exportPath" -ForegroundColor Cyan
        
    } else {
        Write-Host "No servicePrincipalSignInActivities data found" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error testing servicePrincipalSignInActivities: $($_.Exception.Message)" -ForegroundColor Red
}

# Test D30 Summary for comparison
Write-Host ""
Write-Host "COMPARING WITH D30 SUMMARY FORMAT" -ForegroundColor Magenta

try {
    $uri = "https://graph.microsoft.com/beta/reports/getAzureADApplicationSignInSummary(period='D30')?`$top=5"
    $response = Invoke-RestMethod -Uri $uri -Headers $headers
    
    if ($response.value -and $response.value.Count -gt 0) {
        Write-Host "D30 Summary sample data:" -ForegroundColor Yellow
        foreach ($activity in $response.value[0..2]) {
            Write-Host "  App: $($activity.appDisplayName)" -ForegroundColor Gray
            Write-Host "    AppId: $($activity.id)" -ForegroundColor Gray
            Write-Host "    Success Count: $($activity.successfulSignInCount)" -ForegroundColor Gray
            Write-Host "    Failed Count: $($activity.failedSignInCount)" -ForegroundColor Gray
            Write-Host "    Note: D30 Summary does NOT provide lastSignInDateTime" -ForegroundColor Yellow
            Write-Host ""
        }
    }
}
catch {
    Write-Host "Error getting D30 summary: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "RECOMMENDED IMPLEMENTATION STRATEGY" -ForegroundColor Magenta
Write-Host "1. Load servicePrincipalSignInActivities into hashtable (comprehensive historical data)" -ForegroundColor Green
Write-Host "2. Use D30 Summary as fallback for apps not found in step 1" -ForegroundColor Yellow
Write-Host "3. Use audit logs as final fallback for critical missing data" -ForegroundColor Yellow
Write-Host "4. Existing datetime parsing functions are compatible" -ForegroundColor Green

Write-Host ""
Write-Host "DATETIME COMPATIBILITY TEST COMPLETED" -ForegroundColor Cyan
