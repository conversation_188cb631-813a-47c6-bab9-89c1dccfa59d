# Service Principal Sign-In Activities POC - Simple Version
# Comparing getAzureADApplicationSignInSummary vs servicePrincipalSignInActivities

$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
    TestLimit = 500
}

Write-Host "SERVICE PRINCIPAL SIGN-IN ACTIVITIES POC" -ForegroundColor Cyan
Write-Host "Testing with $($Config.TestLimit) service principals" -ForegroundColor Yellow

# Authentication
Write-Host "Authenticating..." -ForegroundColor Yellow
$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

$connection = Invoke-RestMethod -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" -Method POST -Body $body
$token = $connection.access_token
$headers = @{ "Authorization" = "Bearer $token"; "Content-Type" = "application/json" }

Write-Host "Connected to Microsoft Graph" -ForegroundColor Green

# Get Service Principals
Write-Host "Getting service principals..." -ForegroundColor Yellow
$allSPs = @()
$uri = "https://graph.microsoft.com/v1.0/servicePrincipals?`$top=999"

do {
    try {
        $response = Invoke-RestMethod -Uri $uri -Headers $headers
        if ($response.value) {
            $allSPs += $response.value
            $uri = $response.'@odata.nextLink'
            Write-Host "Retrieved $($allSPs.Count) service principals..." -ForegroundColor Gray
            if ($allSPs.Count -ge $Config.TestLimit) { break }
        } else {
            break
        }
    }
    catch {
        Write-Host "Error getting SPs: $($_.Exception.Message)" -ForegroundColor Red
        break
    }
} while ($uri -and $allSPs.Count -lt $Config.TestLimit)

if ($allSPs.Count -gt $Config.TestLimit) {
    $allSPs = $allSPs[0..($Config.TestLimit - 1)]
}

Write-Host "Testing with $($allSPs.Count) service principals" -ForegroundColor Green

# APPROACH 1: D30 Summary
Write-Host ""
Write-Host "APPROACH 1: getAzureADApplicationSignInSummary" -ForegroundColor Magenta
$approach1Results = @{}
$approach1Start = Get-Date

try {
    $uri = "https://graph.microsoft.com/beta/reports/getAzureADApplicationSignInSummary(period='D30')"
    $response = Invoke-RestMethod -Uri $uri -Headers $headers
    
    if ($response.value) {
        foreach ($activity in $response.value) {
            if ($activity.id) {
                $approach1Results[$activity.id] = @{
                    SuccessfulSignInCount = $activity.successfulSignInCount
                    FailedSignInCount = $activity.failedSignInCount
                    AppDisplayName = $activity.appDisplayName
                }
            }
        }
    }
    Write-Host "Found $($approach1Results.Count) apps with D30 activity" -ForegroundColor Green
}
catch {
    Write-Host "Error with Approach 1: $($_.Exception.Message)" -ForegroundColor Red
}

$approach1Duration = ((Get-Date) - $approach1Start).TotalSeconds
Write-Host "Approach 1 completed in $([Math]::Round($approach1Duration, 2)) seconds" -ForegroundColor Cyan

# APPROACH 2: servicePrincipalSignInActivities
Write-Host ""
Write-Host "APPROACH 2: servicePrincipalSignInActivities" -ForegroundColor Magenta
$approach2Results = @{}
$approach2Start = Get-Date

try {
    $uri = "https://graph.microsoft.com/beta/reports/servicePrincipalSignInActivities"
    
    do {
        $response = Invoke-RestMethod -Uri $uri -Headers $headers
        if ($response.value) {
            foreach ($activity in $response.value) {
                if ($activity.appId) {
                    $approach2Results[$activity.appId] = @{
                        AppDisplayName = $activity.appDisplayName
                        ServicePrincipalName = $activity.servicePrincipalName
                        LastSignInDateTime = $activity.lastSignInActivity.lastSignInDateTime
                        DelegatedClientSignInActivity = $activity.delegatedClientSignInActivity
                        ApplicationAuthenticationClientSignInActivity = $activity.applicationAuthenticationClientSignInActivity
                    }
                }
            }
            $uri = $response.'@odata.nextLink'
            Write-Host "Retrieved $($approach2Results.Count) SP activities..." -ForegroundColor Gray
        } else {
            break
        }
    } while ($uri)
    
    Write-Host "Found $($approach2Results.Count) service principals with activities" -ForegroundColor Green
}
catch {
    Write-Host "Error with Approach 2: $($_.Exception.Message)" -ForegroundColor Red
}

$approach2Duration = ((Get-Date) - $approach2Start).TotalSeconds
Write-Host "Approach 2 completed in $([Math]::Round($approach2Duration, 2)) seconds" -ForegroundColor Cyan

# COMPARISON
Write-Host ""
Write-Host "COMPARISON RESULTS" -ForegroundColor Magenta
Write-Host "Performance:" -ForegroundColor Yellow
Write-Host "  Approach 1 (D30): $([Math]::Round($approach1Duration, 2)) seconds" -ForegroundColor Gray
Write-Host "  Approach 2 (SP Activities): $([Math]::Round($approach2Duration, 2)) seconds" -ForegroundColor Gray

Write-Host "Data Coverage:" -ForegroundColor Yellow
Write-Host "  Approach 1: $($approach1Results.Count) applications" -ForegroundColor Gray
Write-Host "  Approach 2: $($approach2Results.Count) service principals" -ForegroundColor Gray

# Check overlap
$approach1AppIds = $approach1Results.Keys
$approach2AppIds = $approach2Results.Keys
$commonAppIds = $approach1AppIds | Where-Object { $approach2AppIds -contains $_ }
$onlyInApproach1 = $approach1AppIds | Where-Object { $approach2AppIds -notcontains $_ }
$onlyInApproach2 = $approach2AppIds | Where-Object { $approach1AppIds -notcontains $_ }

Write-Host "Overlap Analysis:" -ForegroundColor Yellow
Write-Host "  Common to both: $($commonAppIds.Count)" -ForegroundColor Green
Write-Host "  Only in Approach 1: $($onlyInApproach1.Count)" -ForegroundColor Yellow
Write-Host "  Only in Approach 2: $($onlyInApproach2.Count)" -ForegroundColor Yellow

# Test SP Coverage
$matchCount = 0
$approach1OnlyCount = 0
$approach2OnlyCount = 0
$neitherCount = 0

foreach ($sp in $allSPs) {
    $appId = $sp.appId
    $inApproach1 = $approach1Results.ContainsKey($appId)
    $inApproach2 = $approach2Results.ContainsKey($appId)
    
    if ($inApproach1 -and $inApproach2) {
        $matchCount++
    } elseif ($inApproach1) {
        $approach1OnlyCount++
    } elseif ($inApproach2) {
        $approach2OnlyCount++
    } else {
        $neitherCount++
    }
}

Write-Host "Test SP Coverage ($($allSPs.Count) total):" -ForegroundColor Yellow
Write-Host "  Found in both: $matchCount" -ForegroundColor Green
Write-Host "  Only in Approach 1: $approach1OnlyCount" -ForegroundColor Yellow
Write-Host "  Only in Approach 2: $approach2OnlyCount" -ForegroundColor Yellow
Write-Host "  Found in neither: $neitherCount" -ForegroundColor Red

$coverageRatio = [Math]::Round((($matchCount + $approach1OnlyCount + $approach2OnlyCount) / $allSPs.Count) * 100, 2)
Write-Host "  Combined coverage: $coverageRatio%" -ForegroundColor Green

# RECOMMENDATIONS
Write-Host ""
Write-Host "RECOMMENDATIONS" -ForegroundColor Magenta

if ($approach2Duration -lt $approach1Duration) {
    Write-Host "Performance: Approach 2 is FASTER" -ForegroundColor Green
} else {
    Write-Host "Performance: Approach 1 is FASTER" -ForegroundColor Green
}

if ($approach2Results.Count -gt $approach1Results.Count) {
    Write-Host "Coverage: Approach 2 provides MORE data" -ForegroundColor Green
} else {
    Write-Host "Coverage: Approach 1 provides MORE data" -ForegroundColor Green
}

Write-Host ""
Write-Host "Implementation Strategy:" -ForegroundColor Yellow
if ($approach2Results.Count -gt $approach1Results.Count -and $approach2Duration -le ($approach1Duration * 1.5)) {
    Write-Host "  PRIMARY: Use servicePrincipalSignInActivities" -ForegroundColor Green
    Write-Host "  FALLBACK: Keep D30 Summary for specific cases" -ForegroundColor Yellow
} else {
    Write-Host "  PRIMARY: Keep D30 Summary" -ForegroundColor Green
    Write-Host "  SUPPLEMENT: Use servicePrincipalSignInActivities for additional data" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "POC COMPLETED" -ForegroundColor Cyan
