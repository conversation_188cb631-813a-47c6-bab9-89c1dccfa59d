# ===== EMAIL FUNCTION TEST SCRIPT =====
# This script tests the email functionality used in the main Enterprise Application Maintenance script
# Author: AI Assistant
# Version: v1.0
# Date: 2025-01-08

# ===== CONFIGURATION =====
$Config = @{
    # === EMAIL CONFIGURATION ===
    FromEmail = "<EMAIL>"
    ReportRecipients = @("<EMAIL>","<EMAIL>", "<EMAIL>")
    MaxAttachmentSizeMB = 25
    
    # === SMTP CONFIGURATION (may need to be configured) ===
    SMTPServer = "smtp.office365.com"  # Office 365 SMTP server
    SMTPPort = 587
    UseSSL = $true
    
    # === TEST SETTINGS ===
    Debug = $true
    TestMode = $true
}

Write-Host "=== EMAIL FUNCTION TEST SCRIPT ===" -ForegroundColor Cyan
Write-Host "Testing email functionality from Enterprise Application Maintenance script" -ForegroundColor Yellow

# ===== LOGGING FUNCTION =====
function Write-Log {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Message,
        [string]$ForegroundColor = "White"
    )
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    $logMessage = "[$timestamp] $Message"
    Write-Host -Object $logMessage -ForegroundColor $ForegroundColor
}

# ===== EMAIL FUNCTION FROM MAIN SCRIPT =====
function Send-CompletionEmail {
    param(
        [array]$ReportPaths = @(),
        [array]$EmailSummary = @()
    )
    
    try {
        Write-Log "Starting email function test..." -ForegroundColor Yellow
        
        $subject = "TEST: Enterprise Application Maintenance V2 - Enhanced Email Test"
        $totalRunTime = 5.2  # Mock runtime
        
        $body = @"
This is a TEST EMAIL from the Enterprise Application Maintenance V2 - Enhanced script.

=== EXECUTION SUMMARY ===
Total Runtime: $totalRunTime minutes
Script Version: v2.1-enhanced-test
Execution Mode: TEST MODE

=== TEST PROCESSING RESULTS ===
Total Service Principals: 500 (test mode)
Microsoft Apps Skipped: 45
Excluded Types Skipped: 123
Excluded Owners Skipped: 15 (SharePoint Online filtering)
Active Applications: 67
Deletion Candidates: 89
Disabling Candidates: 12
Total Errors: 0

=== OWNER ANALYSIS TEST ===
SharePoint/Office 365 Owners Found: 15 service principals
Owner-based filtering successfully applied to exclude workflow-related SPs.

=== PERFORMANCE METRICS TEST ===
Hashtable lookups: 234 (fast)
API calls to audit logs: 12 (slow)
Skipped due to filtering: 183 (performance boost)
Hashtable efficiency: 95.1% (higher is better)

=== TEST REPORTS GENERATED ===
Enterprise_Applications_Full_Report_TEST.csv
DeletedApps_Report_TEST.csv
DisabledServicePrincipals_TEST.csv

=== EMAIL SUMMARY TEST ===
$($EmailSummary -join "`n")

This is an automated TEST message from the Enterprise Application Maintenance system.
If you receive this email, the email function is working correctly.

Test performed at: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@

        Write-Log "Email configuration:" -ForegroundColor Cyan
        Write-Log "  From: $($Config.FromEmail)" -ForegroundColor Gray
        Write-Log "  To: $($Config.ReportRecipients -join ', ')" -ForegroundColor Gray
        Write-Log "  Subject: $subject" -ForegroundColor Gray
        Write-Log "  SMTP Server: $($Config.SMTPServer)" -ForegroundColor Gray
        Write-Log "  SMTP Port: $($Config.SMTPPort)" -ForegroundColor Gray
        Write-Log "  Use SSL: $($Config.UseSSL)" -ForegroundColor Gray

        # ===== METHOD 1: Try Send-MailMessage (Built-in PowerShell) =====
        Write-Log "Attempting Method 1: Send-MailMessage..." -ForegroundColor Yellow
        
        try {
            # Check if Send-MailMessage is available
            if (Get-Command Send-MailMessage -ErrorAction SilentlyContinue) {
                Write-Log "Send-MailMessage command found, attempting to send..." -ForegroundColor Green
                
                $mailParams = @{
                    From = $Config.FromEmail
                    To = $Config.ReportRecipients
                    Subject = $subject
                    Body = $body
                    SmtpServer = $Config.SMTPServer
                    Port = $Config.SMTPPort
                    UseSsl = $Config.UseSSL
                }
                
                # Note: This will likely fail without proper authentication
                # Send-MailMessage @mailParams
                Write-Log "Send-MailMessage parameters prepared (not sent due to authentication requirements)" -ForegroundColor Yellow
                Write-Log "  This method requires SMTP authentication which may not be configured" -ForegroundColor Yellow
            } else {
                Write-Log "Send-MailMessage command not available" -ForegroundColor Red
            }
        }
        catch {
            Write-Log "Method 1 failed: $($_.Exception.Message)" -ForegroundColor Red
        }

        # ===== METHOD 2: Try Outlook COM Object =====
        Write-Log "Attempting Method 2: Outlook COM Object..." -ForegroundColor Yellow
        
        try {
            $outlook = New-Object -ComObject Outlook.Application
            $mail = $outlook.CreateItem(0)  # 0 = olMailItem
            
            $mail.To = $Config.ReportRecipients -join ";"
            $mail.Subject = $subject
            $mail.Body = $body
            
            if ($Config.Debug) {
                Write-Log "Outlook email prepared (not sent in debug mode)" -ForegroundColor Green
                Write-Log "  To: $($mail.To)" -ForegroundColor Gray
                Write-Log "  Subject: $($mail.Subject)" -ForegroundColor Gray
                Write-Log "  Body length: $($mail.Body.Length) characters" -ForegroundColor Gray
                
                # Save as draft instead of sending
                $mail.Save()
                Write-Log "Email saved as draft in Outlook" -ForegroundColor Green
            } else {
                $mail.Send()
                Write-Log "Email sent via Outlook COM object" -ForegroundColor Green
            }
            
            # Clean up
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mail) | Out-Null
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
            
            return $true
        }
        catch {
            Write-Log "Method 2 failed: $($_.Exception.Message)" -ForegroundColor Red
            Write-Log "  This may indicate Outlook is not installed or not configured" -ForegroundColor Yellow
        }

        # ===== METHOD 3: Try .NET System.Net.Mail =====
        Write-Log "Attempting Method 3: .NET System.Net.Mail..." -ForegroundColor Yellow
        
        try {
            Add-Type -AssemblyName System.Net.Mail
            
            $smtpClient = New-Object System.Net.Mail.SmtpClient($Config.SMTPServer, $Config.SMTPPort)
            $smtpClient.EnableSsl = $Config.UseSSL
            
            # Note: This would need credentials
            # $smtpClient.Credentials = New-Object System.Net.NetworkCredential($username, $password)
            
            $mailMessage = New-Object System.Net.Mail.MailMessage
            $mailMessage.From = $Config.FromEmail
            foreach ($recipient in $Config.ReportRecipients) {
                $mailMessage.To.Add($recipient)
            }
            $mailMessage.Subject = $subject
            $mailMessage.Body = $body
            
            Write-Log ".NET mail message prepared (not sent due to authentication requirements)" -ForegroundColor Yellow
            Write-Log "  This method requires SMTP credentials which are not configured" -ForegroundColor Yellow
            
            # Clean up
            $mailMessage.Dispose()
            $smtpClient.Dispose()
            
        }
        catch {
            Write-Log "Method 3 failed: $($_.Exception.Message)" -ForegroundColor Red
        }

        # ===== SAVE EMAIL CONTENT FOR REVIEW =====
        Write-Log "Saving email content to file for review..." -ForegroundColor Yellow
        
        $emailPath = "Email_Test_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
        $body | Out-File -FilePath $emailPath -Encoding UTF8
        Write-Log "Email content saved to: $emailPath" -ForegroundColor Green
        
        # ===== DIAGNOSTICS =====
        Write-Log "Email diagnostics:" -ForegroundColor Cyan
        Write-Log "  Current user: $($env:USERNAME)" -ForegroundColor Gray
        Write-Log "  Computer name: $($env:COMPUTERNAME)" -ForegroundColor Gray
        Write-Log "  Domain: $($env:USERDOMAIN)" -ForegroundColor Gray
        
        # Check if running in Azure Automation or similar
        if ($env:AUTOMATION_ASSET_ENDPOINT) {
            Write-Log "  Running in Azure Automation context" -ForegroundColor Yellow
        } else {
            Write-Log "  Running in local PowerShell context" -ForegroundColor Gray
        }
        
        return $false  # Return false since we didn't actually send
    }
    catch {
        Write-Log "Error in email function: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# ===== RUN EMAIL TEST =====
Write-Log "Starting email function test..." -ForegroundColor Green

$testEmailSummary = @(
    "[Test] Processing completed successfully",
    "[Test] Performance metrics captured",
    "[Test] Owner analysis completed",
    "[Test] Reports generated"
)

$testReportPaths = @(
    ".\Test_Report_1.csv",
    ".\Test_Report_2.csv",
    ".\Test_Report_3.csv"
)

$emailResult = Send-CompletionEmail -ReportPaths $testReportPaths -EmailSummary $testEmailSummary

Write-Log "" -ForegroundColor White
Write-Log "=== EMAIL TEST RESULTS ===" -ForegroundColor Magenta

if ($emailResult) {
    Write-Log "✓ Email function executed successfully" -ForegroundColor Green
} else {
    Write-Log "✗ Email function completed but did not send email" -ForegroundColor Yellow
}

Write-Log "" -ForegroundColor White
Write-Log "=== RECOMMENDATIONS ===" -ForegroundColor Magenta
Write-Log "1. Check if Outlook is installed and configured on this machine" -ForegroundColor Yellow
Write-Log "2. Verify SMTP server settings and authentication requirements" -ForegroundColor Yellow
Write-Log "3. Check if the machine has internet connectivity to smtp.office365.com:587" -ForegroundColor Yellow
Write-Log "4. Consider using Azure Automation with managed identity for email sending" -ForegroundColor Yellow
Write-Log "5. Review the saved email content file to verify message formatting" -ForegroundColor Yellow

Write-Log "" -ForegroundColor White
Write-Log "Email test completed." -ForegroundColor Cyan
