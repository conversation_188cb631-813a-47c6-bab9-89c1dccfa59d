# Graph API Email Test Script
# Tests email functionality using Microsoft Graph API

$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
    FromEmail = "<EMAIL>"
    ReportRecipients = @("<EMAIL>","<EMAIL>", "<EMAIL>")
    Debug = $false
}

Write-Host "GRAPH API EMAIL TEST" -ForegroundColor Cyan
Write-Host "Using same credentials as main script" -ForegroundColor Yellow

# Authentication
Write-Host "Authenticating to Microsoft Graph..." -ForegroundColor Yellow

$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

try {
    $connection = Invoke-RestMethod -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" -Method POST -Body $body
    $token = $connection.access_token
    Write-Host "Authentication successful" -ForegroundColor Green
}
catch {
    Write-Host "Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Graph Request Function
function Invoke-GraphRequest {
    param(
        [string]$Uri,
        [string]$Method = "GET",
        [string]$Body
    )

    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    try {
        if ($Body) {
            return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $headers -Body $Body
        } else {
            return Invoke-RestMethod -Uri $Uri -Method $Method -Headers $headers
        }
    }
    catch {
        Write-Host "Graph API Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Test permissions
Write-Host "Testing Graph API permissions..." -ForegroundColor Yellow

# Test user access
try {
    $userProfile = Invoke-GraphRequest -Uri "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)"
    if ($userProfile) {
        Write-Host "User profile access: SUCCESS" -ForegroundColor Green
        Write-Host "  Display Name: $($userProfile.displayName)" -ForegroundColor Gray
    } else {
        Write-Host "User profile access: FAILED" -ForegroundColor Red
    }
}
catch {
    Write-Host "User profile test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test mail permissions
Write-Host "Testing mail permissions..." -ForegroundColor Yellow
try {
    $mailboxSettings = Invoke-GraphRequest -Uri "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)/mailboxSettings"
    if ($mailboxSettings) {
        Write-Host "Mail permissions: AVAILABLE" -ForegroundColor Green
    } else {
        Write-Host "Mail permissions: LIMITED" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Mail permissions test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Prepare test email
Write-Host "Preparing test email..." -ForegroundColor Yellow

$subject = "TEST: Enterprise Application Maintenance - Graph API Email"
$bodyText = @"
This is a TEST EMAIL sent via Microsoft Graph API.

Test Results:
- Authentication: SUCCESS
- Graph API Access: SUCCESS
- Email Method: Microsoft Graph API
- From: $($Config.FromEmail)
- Test Time: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

If you receive this email, the Graph API email function is working correctly.
"@

# Prepare recipients
$toRecipients = @()
foreach ($recipient in $Config.ReportRecipients) {
    $toRecipients += @{
        emailAddress = @{
            address = $recipient
        }
    }
}

# Prepare email message
$emailMessage = @{
    message = @{
        subject = $subject
        body = @{
            contentType = "Text"
            content = $bodyText
        }
        toRecipients = $toRecipients
        from = @{
            emailAddress = @{
                address = $Config.FromEmail
            }
        }
    }
    saveToSentItems = $true
}

$emailJson = $emailMessage | ConvertTo-Json -Depth 10

Write-Host "Email prepared:" -ForegroundColor Cyan
Write-Host "  From: $($Config.FromEmail)" -ForegroundColor Gray
Write-Host "  To: $($Config.ReportRecipients -join ', ')" -ForegroundColor Gray
Write-Host "  Subject: $subject" -ForegroundColor Gray

if ($Config.Debug) {
    Write-Host "DEBUG MODE: Saving email to file instead of sending" -ForegroundColor Yellow
    
    $emailPath = "Graph_Email_Debug_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    $emailJson | Out-File -FilePath $emailPath -Encoding UTF8
    Write-Host "Email JSON saved to: $emailPath" -ForegroundColor Green
    
    $readablePath = "Graph_Email_Debug_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    $readableContent = "From: $($Config.FromEmail)`nTo: $($Config.ReportRecipients -join ', ')`nSubject: $subject`n`nBody:`n$bodyText"
    $readableContent | Out-File -FilePath $readablePath -Encoding UTF8
    Write-Host "Readable content saved to: $readablePath" -ForegroundColor Green
    
    Write-Host "To actually send email, set Debug = false" -ForegroundColor Yellow
} else {
    Write-Host "Sending email via Graph API..." -ForegroundColor Yellow
    
    $uri = "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)/sendMail"
    $response = Invoke-GraphRequest -Uri $uri -Method "POST" -Body $emailJson
    
    if ($response -eq $null) {
        Write-Host "Email sent successfully" -ForegroundColor Green
    } else {
        Write-Host "Email send failed" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "TEST COMPLETED" -ForegroundColor Magenta
Write-Host "Check the generated files and console output for results" -ForegroundColor Yellow

Write-Host ""
Write-Host "NEXT STEPS" -ForegroundColor Magenta
Write-Host "1. Review the saved email files" -ForegroundColor Yellow
Write-Host "2. Verify service principal has Mail.Send permissions" -ForegroundColor Yellow
Write-Host "3. Set Debug = false to actually send email" -ForegroundColor Yellow
Write-Host "4. Update main script with working email function" -ForegroundColor Yellow
