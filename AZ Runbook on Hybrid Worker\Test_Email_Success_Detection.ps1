# Test Email Success Detection Fix
# Tests the corrected email success detection logic

$Config = @{
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    ClientSecret = "****************************************"
    FromEmail = "<EMAIL>"
    ReportRecipients = @("<EMAIL>","<EMAIL>", "<EMAIL>")
}

Write-Host "EMAIL SUCCESS DETECTION TEST" -ForegroundColor Cyan

# Authentication
$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

$connection = Invoke-RestMethod -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" -Method POST -Body $body
$token = $connection.access_token
Write-Host "Authentication: SUCCESS" -ForegroundColor Green

# Test the corrected success detection logic
Write-Host "Testing email success detection logic..." -ForegroundColor Yellow

$toRecipients = @()
foreach ($recipient in $Config.ReportRecipients) {
    $toRecipients += @{
        emailAddress = @{
            address = $recipient
        }
    }
}

$emailMessage = @{
    message = @{
        subject = "TEST: Email Success Detection Fix"
        body = @{
            contentType = "Text"
            content = @"
This email tests the corrected success detection logic.

Test Details:
- Timestamp: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
- Purpose: Verify email success detection works correctly
- Expected: Console should show SUCCESS instead of failure

If you receive this email AND the console shows success, the fix is working!
"@
        }
        toRecipients = $toRecipients
        from = @{
            emailAddress = @{
                address = $Config.FromEmail
            }
        }
    }
    saveToSentItems = $true
}

$emailJson = $emailMessage | ConvertTo-Json -Depth 10
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

Write-Host "Sending test email..." -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "https://graph.microsoft.com/v1.0/users/$($Config.FromEmail)/sendMail" -Method POST -Headers $headers -Body $emailJson
    
    Write-Host "Raw response received:" -ForegroundColor Gray
    Write-Host "  Type: $($response.GetType().Name)" -ForegroundColor Gray
    Write-Host "  Value: '$response'" -ForegroundColor Gray
    Write-Host "  Is Null: $($null -eq $response)" -ForegroundColor Gray
    Write-Host "  Is Empty String: $($response -eq '')" -ForegroundColor Gray
    Write-Host "  Is Null or Empty: $([string]::IsNullOrEmpty($response))" -ForegroundColor Gray
    
    # Test the corrected logic
    if ($null -eq $response -or $response -eq "" -or [string]::IsNullOrEmpty($response)) {
        Write-Host "✓ SUCCESS: Email success detection logic working correctly!" -ForegroundColor Green
        Write-Host "✓ Email should have been sent successfully" -ForegroundColor Green
    } else {
        Write-Host "✗ FAILED: Unexpected response received" -ForegroundColor Red
        Write-Host "✗ Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Red
    }
}
catch {
    Write-Host "✗ Email send failed with exception" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "EMAIL SUCCESS DETECTION TEST COMPLETED" -ForegroundColor Magenta
Write-Host "Check your email inbox to confirm delivery" -ForegroundColor Yellow
