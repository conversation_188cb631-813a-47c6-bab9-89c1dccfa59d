# ===== CONFIGURATION =====
$Config = @{
    # === SCRIPT SETTINGS ===
    ScriptVersion = "v2.0"
    Debug = $true                       # If true, operations are logged but not executed
    TestMode = $true                    # If true, process only a small subset for testing
    TestModeLimit = 10                  # Number of items to process in test mode

    # === AUTHENTICATION SETTINGS ===
    TenantId = "bc876b21-f134-4c12-a265-8ed26b7f0f3b"
    ClientId = "022b1a38-6483-4c37-8cbd-d979609322b5"
    #ClientSecret = Get-AutomationVariable -Name 'Application_Maintenance_Secret'
    ClientSecret = "****************************************"

    # === EMAIL CONFIGURATION ===
    FromEmail = "<EMAIL>"
    ReportRecipients = @("<EMAIL>","<EMAIL>", "<EMAIL>")
    MaxAttachmentSizeMB = 25

    # === MAINTENANCE SETTINGS ===
    InactiveThresholdMonths = 12        # Apps inactive for this many months will be disabled
    NewAppGracePeriodDays = 180         # New apps must be this old before being considered for disabling
    DeleteGracePeriodDays = 180         # Recently disabled SPs must wait this long before deletion

    # === MICROSOFT APP EXCLUSION PATTERNS ===
    MicrosoftTenantId = "f8cdef31-a31e-4b4a-93e4-5f571e91255a"
    ExcludedPublisherPatterns = @(
        "*Microsoft*",
        "*Office*",
        "*Azure*",
        "*Windows*",
        "*Xbox*",
        "*Skype*",
        "*OneDrive*",
        "*SharePoint*",
        "*Teams*",
        "*Outlook*",
        "*Exchange*",
        "*Dynamics*",
        "*Power*",
        "*Visual Studio*"
    )
    ExcludedDisplayNamePatterns = @(
        "*Microsoft*",
        "*J01*"
    )

    # === OUTPUT CONFIGURATION ===
    ExportReports = $true               # Export results to CSV files
    SendEmails = $true                  # Send email notifications
    ShowConsole = $true                 # Display results in console
}

$IsAzureFunction = $false

Write-Host "JOB HOST: $($env:COMPUTERNAME)" -ForegroundColor Cyan
Write-Host "Script Version: $($Config.ScriptVersion)" -ForegroundColor Cyan
Write-Host "Debug Mode: $($Config.Debug)" -ForegroundColor Cyan
Write-Host "Test Mode: $($Config.TestMode)" -ForegroundColor Cyan
if ($Config.TestMode) {
    Write-Host "Test Mode Limit: $($Config.TestModeLimit) items per category" -ForegroundColor Yellow
}

# ===== AUTHENTICATION =====
Write-Host "Authenticating to Microsoft Graph..." -ForegroundColor Yellow

$body = @{
    Grant_Type    = "client_credentials"
    Scope         = "https://graph.microsoft.com/.default"
    Client_Id     = $Config.ClientId
    Client_Secret = $Config.ClientSecret
}

$connection = Invoke-RestMethod `
    -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" `
    -Method POST `
    -Body $body

$script:token = $connection.access_token
$script:tokenExpiry = (Get-Date).AddMinutes(50)

Write-Host "Connected to Microsoft Graph" -ForegroundColor Green


# ===== HELPER FUNCTIONS =====

function Write-Log {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Message,
        [string]$ForegroundColor,
        [switch]$NoNewLine
    )

    if ($IsAzureFunction) {
        $timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
        $logMessage = "$timestamp INFO: $Message"
        if ($NoNewLine) {
            Write-Output -NoNewline $logMessage
        } else {
            Write-Output $logMessage
        }
    } else {
        Write-Host -Object $Message -ForegroundColor $ForegroundColor -NoNewline:$NoNewLine
    }
}

function Test-TokenExpiry {
    if ((Get-Date) -gt $script:tokenExpiry) {
        Write-Log "Token expired, refreshing..." -ForegroundColor Yellow

        $body = @{
            Grant_Type    = "client_credentials"
            Scope         = "https://graph.microsoft.com/.default"
            Client_Id     = $Config.ClientId
            Client_Secret = $Config.ClientSecret
        }

        $connection = Invoke-RestMethod `
            -Uri "https://login.microsoftonline.com/$($Config.TenantId)/oauth2/v2.0/token" `
            -Method POST `
            -Body $body

        $script:token = $connection.access_token
        $script:tokenExpiry = (Get-Date).AddMinutes(50)

        Write-Log "Token refreshed" -ForegroundColor Green
    }
}

function Invoke-GraphRequest {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri,
        [string]$Method = "GET",
        [string]$Body,
        [hashtable]$Headers = @{}
    )

    Test-TokenExpiry

    $requestHeaders = @{
        "Authorization" = "Bearer $script:token"
        "Content-Type" = "application/json"
    }

    # Add any additional headers
    foreach ($key in $Headers.Keys) {
        $requestHeaders[$key] = $Headers[$key]
    }

    try {
        if ($Body) {
            $response = Invoke-RestMethod -Uri $Uri -Method $Method -Headers $requestHeaders -Body $Body
        } else {
            $response = Invoke-RestMethod -Uri $Uri -Method $Method -Headers $requestHeaders
        }
        return $response
    }
    catch {
        $errorDetails = $_.Exception.Message
        if ($_.Exception.Response) {
            try {
                $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
                $reader.BaseStream.Position = 0
                $reader.DiscardBufferedData()
                $responseBody = $reader.ReadToEnd()
                $errorDetails += " | Response: $responseBody"
            }
            catch {
                # Ignore errors reading response body
            }
        }
        Write-Log "Error calling Graph API: $Uri - $errorDetails" -ForegroundColor Red
        throw
    }
}

function Get-AllGraphResults {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Uri
    )

    $allResults = @()
    $nextLink = $Uri

    do {
        $response = Invoke-GraphRequest -Uri $nextLink
        if ($response.value) {
            $allResults += $response.value
        }
        $nextLink = $response.'@odata.nextLink'
    } while ($nextLink)

    return $allResults
}

function Test-MicrosoftApp {
    param(
        [Parameter(Mandatory=$true)]
        [object]$ServicePrincipal
    )

    # Check if it's a Microsoft-owned app
    if ($ServicePrincipal.appOwnerOrganizationId -eq $Config.MicrosoftTenantId) {
        return $true
    }

    # Check publisher name patterns
    foreach ($pattern in $Config.ExcludedPublisherPatterns) {
        if ($ServicePrincipal.publisherName -like $pattern) {
            return $true
        }
    }

    # Check display name patterns
    foreach ($pattern in $Config.ExcludedDisplayNamePatterns) {
        if ($ServicePrincipal.displayName -like $pattern) {
            return $true
        }
    }

    return $false
}

function Get-ServicePrincipalNotes {
    param(
        [Parameter(Mandatory=$true)]
        [string]$ServicePrincipalId
    )

    try {
        $uri = "https://graph.microsoft.com/beta/servicePrincipals/$ServicePrincipalId"
        $sp = Invoke-GraphRequest -Uri $uri -Method GET
        return $sp.notes
    }
    catch {
        Write-Log "Warning: Could not retrieve notes for SP $ServicePrincipalId : $_" -ForegroundColor Yellow
        return $null
    }
}

function Set-ServicePrincipalNotes {
    param(
        [Parameter(Mandatory=$true)]
        [string]$ServicePrincipalId,
        [Parameter(Mandatory=$true)]
        [string]$Notes
    )

    try {
        $body = @{
            notes = $Notes
        } | ConvertTo-Json

        $uri = "https://graph.microsoft.com/beta/servicePrincipals/$ServicePrincipalId"
        Invoke-GraphRequest -Uri $uri -Method PATCH -Body $body
        return $true
    }
    catch {
        Write-Log "Error: Could not update notes for SP $ServicePrincipalId : $_" -ForegroundColor Red
        return $false
    }
}

function Test-DeleteGracePeriod {
    param(
        [Parameter(Mandatory=$true)]
        [string]$ServicePrincipalId
    )

    $notes = Get-ServicePrincipalNotes -ServicePrincipalId $ServicePrincipalId
    if (-not $notes) {
        return $true  # No notes means no disabled date, safe to delete
    }

    # Look for disabled date pattern in notes
    if ($notes -match "Disabled on: (\d{4}-\d{2}-\d{2})") {
        $disabledDateStr = $matches[1]
        try {
            $disabledDate = [DateTime]::ParseExact($disabledDateStr, "yyyy-MM-dd", $null)
            $daysSinceDisabled = (Get-Date - $disabledDate).Days

            if ($daysSinceDisabled -lt $Config.DeleteGracePeriodDays) {
                Write-Log "SP was disabled $daysSinceDisabled days ago, within grace period of $($Config.DeleteGracePeriodDays) days" -ForegroundColor Yellow
                return $false
            }
        }
        catch {
            Write-Log "Warning: Could not parse disabled date from notes: $disabledDateStr" -ForegroundColor Yellow
        }
    }

    return $true  # Safe to delete
}

function Add-DisabledDateToNotes {
    param(
        [Parameter(Mandatory=$true)]
        [string]$ServicePrincipalId,
        [Parameter(Mandatory=$true)]
        [string]$DisplayName
    )

    $currentNotes = Get-ServicePrincipalNotes -ServicePrincipalId $ServicePrincipalId
    $disabledDate = Get-Date -Format "yyyy-MM-dd"
    $disabledEntry = "Disabled on: $disabledDate by Enterprise Application Maintenance script"

    if ($currentNotes) {
        # Append to existing notes
        $newNotes = "$currentNotes`n$disabledEntry"
    } else {
        # Create new notes
        $newNotes = $disabledEntry
    }

    if (Set-ServicePrincipalNotes -ServicePrincipalId $ServicePrincipalId -Notes $newNotes) {
        Write-Log "Added disabled date to notes for $DisplayName" -ForegroundColor Green
        return $true
    } else {
        Write-Log "Failed to add disabled date to notes for $DisplayName" -ForegroundColor Red
        return $false
    }
}

function Test-AttachmentSize {
    param([string[]]$AttachmentPaths)

    $totalSizeMB = 0
    $validPaths = @()

    if ($AttachmentPaths) {
        foreach ($path in $AttachmentPaths) {
            if (Test-Path $path) {
                $fileInfo = Get-Item $path
                if ($fileInfo.Length -gt 0) {
                    $fileSizeMB = [Math]::Round($fileInfo.Length / 1MB, 2)
                    $totalSizeMB += $fileSizeMB

                    if ($fileSizeMB -gt $Config.MaxAttachmentSizeMB) {
                        $maxSizeMB = $Config.MaxAttachmentSizeMB
                        Write-Log "Warning: File $($fileInfo.Name) is $fileSizeMB MB and exceeds $maxSizeMB MB limit" -ForegroundColor Yellow
                    } else {
                        $validPaths += $path
                        Write-Log "Attachment validated: $($fileInfo.Name) size $fileSizeMB MB" -ForegroundColor Green
                    }
                }
            }
        }
    }

    Write-Log "Total attachment size: $totalSizeMB MB" -ForegroundColor Cyan
    return @{
        ValidPaths = $validPaths
        TotalSizeMB = $totalSizeMB
        IsValid = ($totalSizeMB -le $Config.MaxAttachmentSizeMB)
    }
}

function Send-EmailWithRetry {
    param(
        [hashtable]$MailMessage,
        [string]$SenderEmail,
        [int]$AttemptNumber = 1
    )

    try {
        Write-Log "Email attempt $AttemptNumber using sender: $SenderEmail" -ForegroundColor Cyan
        Write-Log "Recipients: $($MailMessage.message.toRecipients.emailAddress.address -join ', ')" -ForegroundColor Cyan
        Write-Log "Subject: $($MailMessage.message.subject)" -ForegroundColor Cyan
        Write-Log "Attachments: $($MailMessage.message.attachments.Count) files" -ForegroundColor Cyan

        # Ensure token is fresh before sending
        Test-TokenExpiry

        $jsonBody = $MailMessage | ConvertTo-Json -Depth 10
        $uri = "https://graph.microsoft.com/v1.0/users/$SenderEmail/sendMail"

        Write-Log "Sending POST request to: $uri" -ForegroundColor Gray
        Write-Log "JSON Body (first 500 chars): $($jsonBody.Substring(0, [Math]::Min(500, $jsonBody.Length)))" -ForegroundColor Gray

        # Use direct REST API call like the working test script
        $headers = @{
            "Authorization" = "Bearer $script:token"
            "Content-Type" = "application/json"
        }

        $response = Invoke-RestMethod -Uri $uri -Method POST -Headers $headers -Body $jsonBody

        Write-Log "SUCCESS: Email sent successfully using $SenderEmail" -ForegroundColor Green
        return @{
            Success = $true
            SenderUsed = $SenderEmail
            Response = $response
        }
    }
    catch {
        $errorMessage = $_.Exception.Message
        $statusCode = "Unknown"

        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
        }

        Write-Log "FAILED: Email attempt $AttemptNumber failed with $SenderEmail" -ForegroundColor Red
        Write-Log "Status: $statusCode | Error: $errorMessage" -ForegroundColor Yellow

        return @{
            Success = $false
            SenderUsed = $SenderEmail
            Error = $errorMessage
            StatusCode = $statusCode
        }
    }
}

function Send-ReportEmail {
    param (
        [string[]]$AttachmentPaths
    )

    Write-Log "=== Starting Enhanced Email Report Process ===" -ForegroundColor Magenta
    Write-Log "DEBUG: Function Send-ReportEmail called with $($AttachmentPaths.Count) attachment paths" -ForegroundColor Magenta

    try {
        # Step 1: Validate attachments
        Write-Log "Validating attachments..." -ForegroundColor Yellow
        $attachmentValidation = Test-AttachmentSize -AttachmentPaths $AttachmentPaths

        if (-not $attachmentValidation.IsValid) {
            $totalSize = $attachmentValidation.TotalSizeMB
            $maxSize = $Config.MaxAttachmentSizeMB
            Write-Log "Warning: Total attachment size $totalSize MB exceeds limit $maxSize MB" -ForegroundColor Yellow
            Write-Log "Proceeding with valid attachments only..." -ForegroundColor Yellow
        }

        # Step 2: Prepare email content
        Write-Log "Preparing email content..." -ForegroundColor Yellow

        # Determine recipients based on debug mode - use exact same structure as working test script
        $toRecipients = @()
        if ($Config.Debug) {
            $toRecipients += @{
                emailAddress = @{
                    address = $Config.ReportRecipients[0]
                }
            }
        } else {
            foreach ($recipient in $Config.ReportRecipients) {
                $toRecipients += @{
                    emailAddress = @{
                        address = $recipient
                    }
                }
            }
        }

        # Build email subject
        $emailSubject = "Enterprise Application Maintenance Report - $(Get-Date -Format 'yyyy-MM-dd')"
        if ($Config.Debug) {
            $emailSubject = "[DEBUG] $emailSubject"
        }

        # Build email body with enhanced summary
        $debugInfo = if ($Config.Debug) {
            $recipientList = $Config.ReportRecipients -join ', '
            @"
<p><b>--- DEBUG MODE INFORMATION ---</b></p>
<p>This report was generated in DEBUG mode. No actual changes were made to service principals or applications.</p>
<p><b>Production Recipients:</b> $recipientList</p>
<p><b>--- REPORT CONTENT ---</b></p>
"@
        } else { "" }

        # Build email body with variables to avoid complex interpolation
        $deletionCount = $deletionResults.Count
        $disabledCount = $disabledSPsForReport.Count
        $errorCount = $errorLog.Count
        $scriptVersion = $Config.ScriptVersion
        $executionMode = if ($Config.Debug) { "DEBUG - No changes made" } else { "PRODUCTION" }
        $attachmentCount = $attachmentValidation.ValidPaths.Count
        $attachmentSize = $attachmentValidation.TotalSizeMB
        $inactiveThreshold = $Config.InactiveThresholdMonths
        $gracePeriod = $Config.NewAppGracePeriodDays
        $deleteGracePeriod = $Config.DeleteGracePeriodDays
        $microsoftTenantId = $Config.MicrosoftTenantId

        $emailBody = @"
$debugInfo
<p>Hello,</p>

<p>The Enterprise Application Maintenance script has completed. Please find the summary below:</p>

<p><b>Summary:</b></p>
<ul>
    <li>Deletion Candidates Processed: $deletionCount</li>
    <li>Service Principals Processed for Disabling: $disabledCount</li>
    <li>Errors Encountered: $errorCount</li>
    <li>Script Version: $scriptVersion</li>
    <li>Execution Mode: $executionMode</li>
    <li>Total Attachments: $attachmentCount files ($attachmentSize MB)</li>
</ul>

<p><b>Configuration Used:</b></p>
<ul>
    <li>Inactive Threshold: $inactiveThreshold months</li>
    <li>New App Grace Period: $gracePeriod days</li>
    <li>Delete Grace Period: $deleteGracePeriod days</li>
    <li>Microsoft Tenant ID Excluded: $microsoftTenantId</li>
</ul>

<p>Please find the detailed reports attached.</p>

<p><i>***This is an automated mailbox, please DO NOT reply. In case of any queries or issues, please reach <NAME_EMAIL>.</i></p>

<p>Thank You.</p>
"@

        # Step 3: Construct email message
        $mailMessage = @{
            message = @{
                subject = $emailSubject
                body = @{
                    contentType = 'HTML'
                    content = $emailBody
                }
                toRecipients = $toRecipients
                attachments = @()
            }
        }

        # Step 4: Create email body
        $plainTextBody = "Enterprise Application Maintenance Report - $(Get-Date -Format 'yyyy-MM-dd')`n`n"
        $plainTextBody += "Summary:`n"
        $plainTextBody += "- Deletion Candidates Processed: $deletionCount`n"
        $plainTextBody += "- Service Principals Processed for Disabling: $disabledCount`n"
        $plainTextBody += "- Errors Encountered: $errorCount`n"
        $plainTextBody += "- Execution Mode: $executionMode`n`n"
        $plainTextBody += "Please see attached reports for detailed information.`n"

        # Create base email message
        $mailMessage = @{
            message = @{
                subject = $emailSubject
                body = @{
                    contentType = 'Text'
                    content = $plainTextBody
                }
                toRecipients = $toRecipients
                attachments = @()
            }
        }

        # Step 5: Process valid attachments
        if ($attachmentValidation.ValidPaths.Count -gt 0) {
            $validPathCount = $attachmentValidation.ValidPaths.Count
            Write-Log "Processing $validPathCount valid attachments..." -ForegroundColor Yellow

            foreach ($path in $attachmentValidation.ValidPaths) {
                try {
                    $fileInfo = Get-Item $path
                    $fileName = $fileInfo.Name
                    $base64String = [Convert]::ToBase64String([System.IO.File]::ReadAllBytes($path))

                    $attachment = @{
                        "@odata.type" = "#microsoft.graph.fileAttachment"
                        name = $fileName
                        contentBytes = $base64String
                    }

                    $mailMessage.message.attachments += $attachment
                    Write-Log "SUCCESS: Processed attachment: $fileName" -ForegroundColor Green
                }
                catch {
                    Write-Log "FAILED: Failed to process attachment $path : $_" -ForegroundColor Red
                }
            }
        }

        # Step 6: Send email
        Write-Log "Attempting to send email..." -ForegroundColor Yellow

        $result = Send-EmailWithRetry -MailMessage $mailMessage -SenderEmail $Config.FromEmail -AttemptNumber 1

        if ($result.Success) {
            $emailSent = $true
            $senderUsed = $result.SenderUsed
            Write-Log "SUCCESS: Email sent successfully!" -ForegroundColor Green
        } else {
            $emailSent = $false
            $lastError = $result.Error
            Write-Log "Email sending failed: $lastError" -ForegroundColor Red
        }

        # Step 7: Report results
        if ($emailSent) {
            $recipientList = ($Config.ReportRecipients -join ', ')
            $attachmentCount = $mailMessage.message.attachments.Count

            Write-Log "=== EMAIL SENT SUCCESSFULLY ===" -ForegroundColor Green
            Write-Log "Sender Used: $senderUsed" -ForegroundColor Green
            Write-Log "Recipients: $recipientList" -ForegroundColor Green
            $totalSizeMB = $attachmentValidation.TotalSizeMB
            Write-Log "Attachments: $attachmentCount files ($totalSizeMB MB)" -ForegroundColor Green

            return $true
        } else {
            Write-Log "=== EMAIL SENDING FAILED ===" -ForegroundColor Red
            Write-Log "Error: $lastError" -ForegroundColor Red
            Write-Log "Check Mail.Send permission for the service principal" -ForegroundColor Yellow

            return $false
        }
    }
    catch {
        Write-Log "=== CRITICAL EMAIL ERROR ===" -ForegroundColor Red
        Write-Log "Unexpected error in email process: $($_.Exception.Message)" -ForegroundColor Red
        Write-Log "Email functionality may need review" -ForegroundColor Yellow
        return $false
    }
    finally {
        Write-Log "=== Email Report Process Completed ===" -ForegroundColor Magenta
    }
}


# ===== GET SERVICE PRINCIPAL SIGN-IN ACTIVITIES =====
Write-Log "Getting service principal sign-in activities..." -ForegroundColor Yellow

try {
    # Try different endpoint formats for sign-in activities
    $signInActivities = @()

    # Use the correct endpoint for service principal sign-in activities
    Write-Log "Retrieving service principal sign-in activities..." -ForegroundColor Yellow
    $signInActivities = Get-AllGraphResults -Uri "https://graph.microsoft.com/beta/reports/servicePrincipalSignInActivities"
    Write-Log "Retrieved $($signInActivities.Count) service principal sign-in activities" -ForegroundColor Green

    if ($signInActivities.Count -gt 0) {
        Write-Log "Successfully retrieved $($signInActivities.Count) service principal sign-in activities" -ForegroundColor Green

        # Process sign-in activities and build lookup hash
        $sps_lastsignin = @()
        foreach ($activity in $signInActivities) {
            if ($activity.lastSignInActivity.lastSignInDateTime) {
                $signInActivityType = "unknown"

                if ($activity.delegatedClientSignInActivity.lastSignInDateTime) {
                    $signInActivityType = "delegatedClient"
                }
                elseif ($activity.delegatedResourceSignInActivity.lastSignInDateTime) {
                    $signInActivityType = "delegatedResource"
                }
                elseif ($activity.applicationAuthenticationClientSignInActivity.lastSignInDateTime) {
                    $signInActivityType = "applicationAuthenticationClient"
                }
                elseif ($activity.applicationAuthenticationResourceSignInActivity.lastSignInDateTime) {
                    $signInActivityType = "applicationAuthenticationResource"
                }

                $sps_lastsignin += [PSCustomObject]@{
                    appId = $activity.appId
                    LastSignInActivity = $activity.lastSignInActivity
                    SignInActivityType = $signInActivityType
                }
            }
        }

        Write-Log "Found SignInActivity for $($sps_lastsignin.Count) service principals" -ForegroundColor Green
        Write-Log "Building hash table for quick lookup..." -ForegroundColor Yellow

        # Build hash table for quick lookup
        $sps_lastsignin_hash = @{}
        foreach ($sp in $sps_lastsignin) {
            $sps_lastsignin_hash[$sp.appId] = @{
                LastSignInDateTime = $sp.LastSignInActivity.lastSignInDateTime
                SignInActivityType = $sp.SignInActivityType
            }
        }

        Write-Log "Sign-in activity lookup table created" -ForegroundColor Green
    } else {
        Write-Log "No sign-in activities retrieved - all service principals will be considered for processing" -ForegroundColor Yellow
        $sps_lastsignin_hash = @{}
    }
}
catch {
    Write-Log "Warning: Could not retrieve service principal sign-in activities" -ForegroundColor Yellow
    Write-Log "This requires AuditLog.Read.All or Reports.Read.All permissions" -ForegroundColor Yellow
    Write-Log "Continuing without sign-in activity data..." -ForegroundColor Yellow
    $sps_lastsignin_hash = @{}
}




# ===== DELETION OF DISABLED SERVICE PRINCIPALS AND APP REGISTRATIONS =====

Write-Log "Getting all disabled service principals..." -ForegroundColor Yellow
$deletionResults = @()
$errorLog = @()

try {
    # Get disabled service principals with expanded owners
    $uri = "https://graph.microsoft.com/beta/servicePrincipals?`$filter=servicePrincipalType eq 'Application' and accountEnabled eq false&`$expand=owners"
    $allDisabledSPs = Get-AllGraphResults -Uri $uri

    Write-Log "Retrieved $($allDisabledSPs.Count) disabled service principals" -ForegroundColor Green

    # Filter out Microsoft apps
    $disabledSPs = @()
    foreach ($sp in $allDisabledSPs) {
        if (-not (Test-MicrosoftApp -ServicePrincipal $sp)) {
            # Get owner information
            $ownerNames = @()
            if ($sp.owners) {
                foreach ($owner in $sp.owners) {
                    try {
                        if ($owner.'@odata.type' -eq '#microsoft.graph.user') {
                            $ownerNames += $owner.userPrincipalName
                        }
                    }
                    catch {
                        Write-Log "Warning: Could not get owner info for $($owner.id)" -ForegroundColor Yellow
                    }
                }
            }

            $disabledSPs += [PSCustomObject]@{
                Id = $sp.id
                DisplayName = $sp.displayName
                ServicePrincipalType = $sp.servicePrincipalType
                AccountEnabled = $sp.accountEnabled
                PublisherName = $sp.publisherName
                AppId = $sp.appId
                AppDisplayName = $sp.appDisplayName
                AppRoleAssignmentRequired = $sp.appRoleAssignmentRequired
                SignInAudience = $sp.signInAudience
                CreatedDateTime = if ($sp.createdDateTime) { [datetime]$sp.createdDateTime } else { $null }
                LastSignInDateTime = if ($sps_lastsignin_hash[$sp.appId]) { [datetime]$sps_lastsignin_hash[$sp.appId].LastSignInDateTime } else { $null }
                SignInActivityType = if ($sps_lastsignin_hash[$sp.appId]) { $sps_lastsignin_hash[$sp.appId].SignInActivityType } else { $null }
                Owner = ($ownerNames -join ";")
            }
        }
    }

    Write-Log "Found $($disabledSPs.Count) non-Microsoft disabled service principals" -ForegroundColor Green
}
catch {
    Write-Log "Error retrieving disabled service principals: $_" -ForegroundColor Red
    $errorLog += "Error retrieving disabled service principals: $_"
    $disabledSPs = @()
}

# Apply TestMode limit if enabled
if ($Config.TestMode -and $disabledSPs.Count -gt $Config.TestModeLimit) {
    Write-Log "TEST MODE: Limiting disabled SPs from $($disabledSPs.Count) to $($Config.TestModeLimit)" -ForegroundColor Magenta
    $disabledSPs = $disabledSPs | Select-Object -First $Config.TestModeLimit
}

foreach ($sp in $disabledSPs) {
    try {
        Write-Log "Processing Service Principal: $($sp.DisplayName)" -ForegroundColor Yellow

        # Find corresponding app registration
        $appReg = $null
        try {
            $appRegUri = "https://graph.microsoft.com/beta/applications?`$filter=appId eq '$($sp.AppId)'"
            $appRegResponse = Invoke-GraphRequest -Uri $appRegUri
            if ($appRegResponse.value -and $appRegResponse.value.Count -gt 0) {
                $appReg = $appRegResponse.value[0]
            }
        }
        catch {
            Write-Log "Warning: Could not query app registration for $($sp.AppId): $_" -ForegroundColor Yellow
        }

        # Check delete grace period
        $canDelete = Test-DeleteGracePeriod -ServicePrincipalId $sp.Id

        # Create result object
        $resultObj = [PSCustomObject]@{
            Timestamp = (Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
            ResourceType = "Service Principal"
            DisplayName = $sp.DisplayName
            AppId = $sp.AppId
            ObjectId = $sp.Id
            CreatedDateTime = $sp.createdDateTime
            PublisherName = $sp.publisherName
            CorrespondingAppRegFound = if ($appReg) { "Yes" } else { "No" }
            CorrespondingAppRegDisplayName = if ($appReg) { $appReg.displayName } else { "N/A" }
            CorrespondingAppRegId = if ($appReg) { $appReg.id } else { "N/A" }
            WithinGracePeriod = if ($canDelete) { "No" } else { "Yes" }
            DeletionStatus = "Pending"
            ErrorMessage = ""
        }

        if (-not $canDelete) {
            Write-Log "Skipping deletion of $($sp.DisplayName) - within grace period" -ForegroundColor Yellow
            $resultObj.DeletionStatus = "Skipped - Within Grace Period"
        }
        elseif ($Config.Debug) {
            Write-Log "DEBUG MODE - Would delete Service Principal: $($sp.DisplayName)" -ForegroundColor Cyan
            $resultObj.DeletionStatus = "Debug - Not Executed"

            if ($appReg) {
                Write-Log "DEBUG MODE - Would delete corresponding App Registration: $($appReg.displayName)" -ForegroundColor Cyan
            }
        }
        else {
            # Delete the service principal
            Write-Log "Deleting Service Principal: $($sp.DisplayName)" -ForegroundColor Red
            try {
                Invoke-GraphRequest -Uri "https://graph.microsoft.com/beta/servicePrincipals/$($sp.Id)" -Method DELETE
                $resultObj.DeletionStatus = "Success"

                # If app registration exists, delete it
                if ($appReg) {
                    Write-Log "Deleting corresponding App Registration: $($appReg.displayName)" -ForegroundColor Red
                    Invoke-GraphRequest -Uri "https://graph.microsoft.com/beta/applications/$($appReg.id)" -Method DELETE
                }
            }
            catch {
                $resultObj.DeletionStatus = "Failed"
                $resultObj.ErrorMessage = $_.Exception.Message
                Write-Log "Error deleting $($sp.DisplayName): $_" -ForegroundColor Red
                $errorLog += "Error deleting $($sp.DisplayName): $_"
            }
        }

        Write-Log "Processed $($sp.DisplayName)" -ForegroundColor Green
    }
    catch {
        $resultObj.DeletionStatus = "Failed"
        $resultObj.ErrorMessage = $_.Exception.Message
        Write-Log "Error processing $($sp.DisplayName): $_" -ForegroundColor Red
        $errorLog += "Error processing $($sp.DisplayName): $_"
    }

    # Add result to array
    $deletionResults += $resultObj
}

Write-Log "Deletion process completed" -ForegroundColor Green

# ===== DISABLING OF STALE SERVICE PRINCIPALS =====

Write-Log "Getting all active service principals..." -ForegroundColor Yellow
$disabledSPsForReport = @()

try {
    # Get active service principals with expanded owners
    $uri = "https://graph.microsoft.com/beta/servicePrincipals?`$filter=servicePrincipalType eq 'Application' and accountEnabled eq true&`$expand=owners"
    $allActiveSPs = Get-AllGraphResults -Uri $uri

    Write-Log "Retrieved $($allActiveSPs.Count) active service principals" -ForegroundColor Green

    # Filter out Microsoft apps and build processed list
    $activeSPs = @()
    foreach ($sp in $allActiveSPs) {
        if (-not (Test-MicrosoftApp -ServicePrincipal $sp)) {
            # Get owner information
            $ownerNames = @()
            if ($sp.owners) {
                foreach ($owner in $sp.owners) {
                    try {
                        if ($owner.'@odata.type' -eq '#microsoft.graph.user') {
                            $ownerNames += $owner.userPrincipalName
                        }
                    }
                    catch {
                        Write-Log "Warning: Could not get owner info for $($owner.id)" -ForegroundColor Yellow
                    }
                }
            }

            $activeSPs += [PSCustomObject]@{
                Id = $sp.id
                DisplayName = $sp.displayName
                ServicePrincipalType = $sp.servicePrincipalType
                AccountEnabled = $sp.accountEnabled
                PublisherName = $sp.publisherName
                AppId = $sp.appId
                AppDisplayName = $sp.appDisplayName
                AppRoleAssignmentRequired = $sp.appRoleAssignmentRequired
                SignInAudience = $sp.signInAudience
                CreatedDateTime = if ($sp.createdDateTime) { [datetime]$sp.createdDateTime } else { $null }
                LastSignInDateTime = if ($sps_lastsignin_hash[$sp.appId]) { [datetime]$sps_lastsignin_hash[$sp.appId].LastSignInDateTime } else { $null }
                SignInActivityType = if ($sps_lastsignin_hash[$sp.appId]) { $sps_lastsignin_hash[$sp.appId].SignInActivityType } else { $null }
                Owner = ($ownerNames -join ";")
            }
        }
    }

    Write-Log "Found $($activeSPs.Count) non-Microsoft active service principals" -ForegroundColor Green
}
catch {
    Write-Log "Error retrieving active service principals: $_" -ForegroundColor Red
    $errorLog += "Error retrieving active service principals: $_"
    $activeSPs = @()
}

# Calculate cutoff dates using config values
$cutoffDate = (Get-Date).AddMonths(-$Config.InactiveThresholdMonths)
$newAppCutoffDate = (Get-Date).AddDays(-$Config.NewAppGracePeriodDays)

Write-Log "Using cutoff date: $($cutoffDate.ToString('yyyy-MM-dd')) for inactive apps" -ForegroundColor Yellow
Write-Log "Using new app grace period: $($newAppCutoffDate.ToString('yyyy-MM-dd'))" -ForegroundColor Yellow

$inactiveSPs = $activeSPs | Where-Object {
    (!$_.LastSignInDateTime -or $_.LastSignInDateTime -lt $cutoffDate) -and
    $_.AccountEnabled -eq $true -and
    $_.CreatedDateTime -lt $newAppCutoffDate
}

Write-Log "Found $($inactiveSPs.Count) inactive service principals to process" -ForegroundColor Yellow

# Apply TestMode limit if enabled
if ($Config.TestMode -and $inactiveSPs.Count -gt $Config.TestModeLimit) {
    Write-Log "TEST MODE: Limiting inactive SPs from $($inactiveSPs.Count) to $($Config.TestModeLimit)" -ForegroundColor Magenta
    $inactiveSPs = $inactiveSPs | Select-Object -First $Config.TestModeLimit
}

foreach ($sp in $inactiveSPs) {
    try {
        # Check token expiry and refresh if needed
        Test-TokenExpiry

        Write-Log "Processing inactive SP: $($sp.DisplayName)" -ForegroundColor Yellow

        if ($Config.Debug) {
            Write-Log "DEBUG MODE - Would disable inactive SP: $($sp.DisplayName)" -ForegroundColor Cyan
            Write-Log "DEBUG MODE - Last sign-in: $($sp.LastSignInDateTime)" -ForegroundColor Cyan
            Write-Log "DEBUG MODE - Created: $($sp.CreatedDateTime)" -ForegroundColor Cyan

            # Add to disabled list for reporting
            $disabledSPsForReport += [PSCustomObject]@{
                DisplayName = $sp.DisplayName
                AppId = $sp.AppId
                ObjectId = $sp.Id
                CreatedDateTime = $sp.CreatedDateTime
                PublisherName = $sp.publisherName
                LastSignIn = $sp.LastSignInDateTime
                DisabledDate = Get-Date
                Status = "Debug - Not Executed"
            }
        }
        else {
            Write-Log "Disabling inactive SP: $($sp.DisplayName)" -ForegroundColor Red

            # Disable SP using Graph API
            $updateBody = @{
                accountEnabled = $false
            } | ConvertTo-Json

            try {
                Invoke-GraphRequest -Uri "https://graph.microsoft.com/beta/servicePrincipals/$($sp.Id)" -Method PATCH -Body $updateBody

                # Add disabled date to notes
                Add-DisabledDateToNotes -ServicePrincipalId $sp.Id -DisplayName $sp.DisplayName

                # Add to disabled list
                $disabledSPsForReport += [PSCustomObject]@{
                    DisplayName = $sp.DisplayName
                    AppId = $sp.AppId
                    ObjectId = $sp.Id
                    CreatedDateTime = $sp.CreatedDateTime
                    PublisherName = $sp.publisherName
                    LastSignIn = $sp.LastSignInDateTime
                    DisabledDate = Get-Date
                    Status = "Successfully Disabled"
                }

                Write-Log "Successfully disabled SP: $($sp.DisplayName)" -ForegroundColor Green
            }
            catch {
                $errorMessage = "Error disabling SP $($sp.DisplayName): $_"
                Write-Log $errorMessage -ForegroundColor Red
                $errorLog += $errorMessage

                $disabledSPsForReport += [PSCustomObject]@{
                    DisplayName = $sp.DisplayName
                    AppId = $sp.AppId
                    ObjectId = $sp.Id
                    CreatedDateTime = $sp.CreatedDateTime
                    PublisherName = $sp.publisherName
                    LastSignIn = $sp.LastSignInDateTime
                    DisabledDate = Get-Date
                    Status = "Failed - $($_.Exception.Message)"
                }
            }
        }
    }
    catch {
        $errorMessage = "Error processing SP $($sp.DisplayName): $_"
        Write-Log $errorMessage -ForegroundColor Red
        $errorLog += $errorMessage
    }
}
# ===== EXPORT RESULTS =====
Write-Log "Exporting results..." -ForegroundColor Yellow

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$reportPaths = @()

# Use temp directory for file exports (Hybrid Worker compatible)
$exportDir = if ($IsAzureFunction -or $env:COMPUTERNAME -like "*HybridWorker*") {
    $env:TEMP
} else {
    (Get-Location).Path
}

# Export deletion results
if ($Config.ExportReports -and $deletionResults.Count -gt 0) {
    $deletedcsvPath = Join-Path $exportDir "DeletedApps_Report_$timestamp.csv"
    try {
        $deletionResults | Export-Csv -Path $deletedcsvPath -NoTypeInformation
        Write-Log "Exported deletion results to: $deletedcsvPath" -ForegroundColor Green
        $reportPaths += $deletedcsvPath
    }
    catch {
        Write-Log "Error exporting deletion results: $_" -ForegroundColor Red
        $errorLog += "Error exporting deletion results: $_"
    }
}

# Export disabled SPs
if ($Config.ExportReports -and $disabledSPsForReport.Count -gt 0) {
    $disabledSPsPath = Join-Path $exportDir "DisabledServicePrincipals_$timestamp.csv"
    try {
        $disabledSPsForReport | Export-Csv -Path $disabledSPsPath -NoTypeInformation
        Write-Log "Exported disabled service principals to: $disabledSPsPath" -ForegroundColor Green
        $reportPaths += $disabledSPsPath
    }
    catch {
        Write-Log "Error exporting disabled SPs: $_" -ForegroundColor Red
        $errorLog += "Error exporting disabled SPs: $_"
    }
}

# Export error log
if ($Config.ExportReports -and $errorLog.Count -gt 0) {
    $errorLogPath = Join-Path $exportDir "Error_$timestamp.txt"
    try {
        $errorLog | Out-File -FilePath $errorLogPath
        Write-Log "Exported error log to: $errorLogPath" -ForegroundColor Green
        $reportPaths += $errorLogPath
    }
    catch {
        Write-Log "Error exporting error log: $_" -ForegroundColor Red
    }
}

# ===== SEND EMAIL REPORT =====
if ($Config.SendEmails) {
    Write-Log "=== EMAIL REPORT PROCESS STARTING ===" -ForegroundColor Cyan
    Write-Log "Sender: $($Config.FromEmail)" -ForegroundColor Cyan
    Write-Log "Recipients: $($Config.ReportRecipients -join ', ')" -ForegroundColor Cyan
    Write-Log "Attachment Paths: $($reportPaths -join ', ')" -ForegroundColor Cyan

    try {
        Write-Log "DEBUG: About to call Send-ReportEmail function" -ForegroundColor Magenta
        $emailSuccess = Send-ReportEmail -AttachmentPaths $reportPaths
        Write-Log "DEBUG: Send-ReportEmail returned: $emailSuccess" -ForegroundColor Magenta

        if ($emailSuccess) {
            Write-Log "SUCCESS: Email report sent successfully" -ForegroundColor Green
        } else {
            Write-Log "FAILED: Email report failed to send" -ForegroundColor Red
            Write-Log "Reports are still available in: $exportDir" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Log "ERROR: Exception in Send-ReportEmail: $($_.Exception.Message)" -ForegroundColor Red
        Write-Log "FAILED: Email report failed to send due to exception" -ForegroundColor Red
        Write-Log "Reports are still available in: $exportDir" -ForegroundColor Yellow
    }
} else {
    Write-Log "Email notifications disabled in configuration" -ForegroundColor Gray
}

# ===== COMPLETION STATUS =====
Write-Log "`n===== SCRIPT COMPLETION STATUS =====" -ForegroundColor Cyan
Write-Log "Processed deletion candidates: $($deletionResults.Count)" -ForegroundColor Green
Write-Log "Processed disabling candidates: $($disabledSPsForReport.Count)" -ForegroundColor Green
Write-Log "Errors encountered: $($errorLog.Count)" -ForegroundColor $(if ($errorLog.Count -gt 0) { "Yellow" } else { "Green" })

if ($Config.Debug) {
    Write-Log "Debug mode: Operations logged but not executed" -ForegroundColor Cyan
} else {
    Write-Log "Production mode: Operations executed" -ForegroundColor Green
}

if ($Config.ExportReports) {
    Write-Log "Report export: Enabled ($($reportPaths.Count) files)" -ForegroundColor Green
} else {
    Write-Log "Report export: Disabled" -ForegroundColor Gray
}

if ($Config.SendEmails) {
    Write-Log "Email notifications: Enabled" -ForegroundColor Green
} else {
    Write-Log "Email notifications: Disabled" -ForegroundColor Gray
}

Write-Log "Enterprise Application Maintenance script completed successfully." -ForegroundColor Green
