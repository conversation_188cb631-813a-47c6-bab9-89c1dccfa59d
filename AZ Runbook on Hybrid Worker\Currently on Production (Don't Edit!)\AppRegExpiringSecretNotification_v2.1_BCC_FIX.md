# App Registration Expiring Secret Notification Script v2.1 - BCC Fix

## Issue Identified in v2
The v2 script had a critical issue with the `bccRecipients` property in the email payload. The Graph API was returning:

```
"Property bccRecipients in payload has a value that does not match schema."
```

## Root Cause
The `bccRecipients` array was being constructed inconsistently:
- When there were no owner emails: `@()` (empty array)
- When there were owner emails: `@(@{ emailAddress = @{ address = $Config.IAMEmail } })` (array with hashtable)

The Graph API expects a consistent array structure.

## Fix Applied in v2 (Updated)
1. **Consistent BCC Array Construction**: Fixed the BCC recipients to always use the same structure
2. **Improved Debug Display**: Fixed the debug information display for BCC recipients
3. **Better Logging**: Fixed the BCC logging to handle the array structure correctly

## Specific Changes Made

### Lines 264-268 (BCC Recipients Construction)
```powershell
# OLD (Problematic):
$originalBccRecipients = if ($validOwnerEmails.Count -gt 0) {
    @(@{ emailAddress = @{ address = $Config.IAMEmail } })
} else {
    @() # No BCC if the IAM email is already the main recipient
}

# NEW (Fixed):
# Build BCC recipients array consistently
$originalBccRecipients = @()
if ($validOwnerEmails.Count -gt 0) {
    $originalBccRecipients += @{ emailAddress = @{ address = $Config.IAMEmail } }
}
# If no owner emails, IAM email is already the main recipient, so no BCC needed
```

### Lines 276-291 (Debug and Logging)
```powershell
# NEW: Fixed debug BCC display
$originalBccDisplay = if ($originalBccRecipients.Count -gt 0) {
    ($originalBccRecipients | ForEach-Object { $_.emailAddress.address }) -join ', '
} else {
    "None"
}

# NEW: Fixed BCC logging
if ($bccRecipients.Count -gt 0) {
    $bccDisplay = ($bccRecipients | ForEach-Object { $_.emailAddress.address }) -join ', '
    Write-Log "Email BCC: $bccDisplay" -ForegroundColor Green
}
```

## Testing Status
The v2 script has been updated with these fixes. The corrected version should now:
1. ✅ Send emails to app owners when Debug = false
2. ✅ Send <NAME_EMAIL> when there are owner emails
3. ✅ Send <NAME_EMAIL> when there are no owner emails
4. ✅ Provide proper debug information
5. ✅ Handle the Graph API schema requirements correctly

## Deployment
The existing `AppRegExpiringSecretNotification_v2.ps1` file has been updated with these fixes. No additional files needed - just use the corrected v2 script.

## Expected Behavior After Fix
- **Apps with owners**: Email sent to owners, <NAME_EMAIL>
- **Apps without owners**: Email sent <NAME_EMAIL> (no BCC)
- **Debug mode**: All emails <NAME_EMAIL> with debug info
- **Logging**: Clear indication of recipients and BCC addresses

The BCC schema error should now be resolved.
