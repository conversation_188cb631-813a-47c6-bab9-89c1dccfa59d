﻿{
    "saveToSentItems":  true,
    "message":  {
                    "subject":  "Enterprise Application Maintenance V2 - Enhanced Completed",
                    "from":  {
                                 "emailAddress":  {
                                                      "address":  "<EMAIL>"
                                                  }
                             },
                    "body":  {
                                 "contentType":  "Text",
                                 "content":  "Enterprise Application Maintenance V2 - Enhan<PERSON> has completed successfully.\n\n=== EXECUTION SUMMARY ===\nTotal Runtime: 56.9 minutes\nScript Version: v2.1-enhanced\nExecution Mode: DEBUG - No changes made\n\n=== PROCESSING RESULTS ===\nTotal Service Principals: 13724\nMicrosoft Apps Skipped: 1035\nExcluded Types Skipped: 9989\nExcluded Owners Skipped: 384\nActive Applications: 736\nDeletion Candidates: 1489\nDisabling Candidates: 91\nTotal Errors: 0\n\n=== OWNER ANALYSIS ===\nSharePoint/Office 365 Owners Found: 384 service principals\nOwner-based filtering successfully applied to exclude workflow-related SPs.\n\n=== PERFORMANCE METRICS ===\n[23:12:37 | +3.1m] Processing 13724 service principals for comprehensive report...\n[00:06:24 | +56.9m] Completed processing 13724 service principals in 53.8 minutes\n[00:06:24 | +56.9m] Errors encountered: 0\n[00:06:24 | +56.9m] Performance Analysis:\n[00:06:24 | +56.9m]   Hashtable lookups: 641 (fast)\n[00:06:24 | +56.9m]   API calls to audit logs: 186 (slow)\n[00:06:24 | +56.9m]   Skipped due to filtering: 11408 (performance boost)\n[00:06:24 | +56.9m]   Hashtable efficiency: 4.7% (higher is better)\n[00:06:24 | +56.9m] Microsoft apps skipped: 1035\n[00:06:24 | +56.9m] Excluded types skipped: 9989\n[00:06:24 | +56.9m] Excluded owners skipped: 384 (SharePoint Online filtering)\n[00:06:24 | +56.9m] Total skipped: 11408\n[00:06:24 | +56.9m] Deletion candidates: 1489\n[00:06:24 | +56.9m] Disabling candidates: 91\n\n=== REPORTS GENERATED ===\n.\\Enterprise_Applications_Full_Report_20250803_000624.csv\n.\\DeletedApps_Report_20250803_000624.csv\n.\\DisabledServicePrincipals_20250803_000624.csv\n\nThis is an automated message from the Enterprise Application Maintenance system."
                             },
                    "toRecipients":  [
                                         {
                                             "emailAddress":  {
                                                                  "address":  "<EMAIL>"
                                                              }
                                         },
                                         {
                                             "emailAddress":  {
                                                                  "address":  "<EMAIL>"
                                                              }
                                         },
                                         {
                                             "emailAddress":  {
                                                                  "address":  "<EMAIL>"
                                                              }
                                         }
                                     ]
                }
}
