# Test PowerShell 5.1 Compatibility
# Tests the fixes for Join-String and Unicode character issues

Write-Host "POWERSHELL 5.1 COMPATIBILITY TEST" -ForegroundColor Cyan
Write-Host "Testing fixes for Join-String and Unicode character issues..." -ForegroundColor Yellow

# Test 1: Join-String replacement
Write-Host ""
Write-Host "Test 1: Array joining (Join-String replacement)" -ForegroundColor Green

$testArray = @("file1.csv", "file2.csv", "file3.csv")

# Old method (would fail in PS 5.1):
# $result = $testArray | Join-String -Separator ', '

# New method (PS 5.1 compatible):
$result = $testArray -join ', '

Write-Host "  Array: $($testArray -join ' | ')" -ForegroundColor White
Write-Host "  Joined result: $result" -ForegroundColor White
Write-Host "  Test 1: PASSED" -ForegroundColor Green

# Test 2: Complex object property joining
Write-Host ""
Write-Host "Test 2: Object property joining" -ForegroundColor Green

$testObjects = @(
    @{ name = "report1.csv"; size = "100KB" },
    @{ name = "report2.csv"; size = "200KB" },
    @{ name = "report3.csv"; size = "50KB" }
)

# Old method (would fail in PS 5.1):
# $objectResult = $testObjects | ForEach-Object { $_.name } | Join-String -Separator ', '

# New method (PS 5.1 compatible):
$objectResult = ($testObjects | ForEach-Object { $_.name }) -join ', '

Write-Host "  Objects: $($testObjects.Count) items" -ForegroundColor White
Write-Host "  Names joined: $objectResult" -ForegroundColor White
Write-Host "  Test 2: PASSED" -ForegroundColor Green

# Test 3: Write-Log function with ASCII-safe characters
Write-Host ""
Write-Host "Test 3: ASCII-safe logging messages" -ForegroundColor Green

function Write-Log {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Message,
        [string]$ForegroundColor = "White"
    )
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    $logMessage = "[$timestamp] $Message"
    Write-Host -Object $logMessage -ForegroundColor $ForegroundColor
}

# Test ASCII-safe success messages
Write-Log "[SUCCESS] Email notification sent successfully" -ForegroundColor Green
Write-Log "[SUCCESS] Recipients: <EMAIL>, <EMAIL>" -ForegroundColor Green
Write-Log "[WARNING] Email notification encountered an issue" -ForegroundColor Yellow
Write-Log "[ERROR] Email send failed" -ForegroundColor Red

Write-Host "  Test 3: PASSED" -ForegroundColor Green

# Test 4: PowerShell version compatibility check
Write-Host ""
Write-Host "Test 4: PowerShell version compatibility" -ForegroundColor Green

Write-Host "  PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
Write-Host "  PowerShell Edition: $($PSVersionTable.PSEdition)" -ForegroundColor White

if ($PSVersionTable.PSVersion.Major -ge 6) {
    Write-Host "  Join-String cmdlet: Available" -ForegroundColor Green
} else {
    Write-Host "  Join-String cmdlet: Not available (using -join operator instead)" -ForegroundColor Yellow
}

if ($PSVersionTable.PSVersion.Major -ge 5) {
    Write-Host "  Script compatibility: GOOD" -ForegroundColor Green
} else {
    Write-Host "  Script compatibility: MAY HAVE ISSUES" -ForegroundColor Red
}

Write-Host "  Test 4: PASSED" -ForegroundColor Green

# Test 5: Environment detection compatibility
Write-Host ""
Write-Host "Test 5: Environment detection" -ForegroundColor Green

$env_checks = @{
    "TERM_PROGRAM" = $env:TERM_PROGRAM
    "COMPUTERNAME" = $env:COMPUTERNAME
    "USERNAME" = $env:USERNAME
    "USERDOMAIN" = $env:USERDOMAIN
}

foreach ($check in $env_checks.GetEnumerator()) {
    if ($check.Value) {
        Write-Host "  $($check.Key): $($check.Value)" -ForegroundColor White
    } else {
        Write-Host "  $($check.Key): (not set)" -ForegroundColor Gray
    }
}

Write-Host "  Test 5: PASSED" -ForegroundColor Green

Write-Host ""
Write-Host "ALL COMPATIBILITY TESTS PASSED" -ForegroundColor Magenta
Write-Host "The script should now work correctly in PowerShell 5.1" -ForegroundColor Green
