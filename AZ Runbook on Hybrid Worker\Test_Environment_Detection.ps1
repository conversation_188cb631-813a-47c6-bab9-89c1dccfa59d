# Test Environment Detection
# Tests the automatic environment detection functionality

Write-Host "ENVIRONMENT DETECTION TEST" -ForegroundColor Cyan
Write-Host "Testing automatic detection of execution environment..." -ForegroundColor Yellow

# ===== ENVIRONMENT DETECTION FUNCTION =====
function Get-ExecutionEnvironment {
    $environment = @{
        Type = "Unknown"
        IsAzureFunction = $false
        IsAzureAutomation = $false
        IsHybridWorker = $false
        IsPowerShellISE = $false
        IsVSCode = $false
        IsLocalTerminal = $false
        Details = @{}
    }

    # Check for Azure Functions
    if ($env:FUNCTIONS_WORKER_RUNTIME -or $env:AzureWebJobsStorage -or $env:WEBSITE_SITE_NAME) {
        $environment.Type = "Azure Functions"
        $environment.IsAzureFunction = $true
        $environment.Details.SiteName = $env:WEBSITE_SITE_NAME
        $environment.Details.WorkerRuntime = $env:FUNCTIONS_WORKER_RUNTIME
        return $environment
    }

    # Check for Azure Automation (Cloud Runbook)
    if ($env:AUTOMATION_ASSET_ENDPOINT -or $env:AutomationAccountName) {
        $environment.Type = "Azure Automation Runbook"
        $environment.IsAzureAutomation = $true
        $environment.Details.AccountName = $env:AutomationAccountName
        $environment.Details.AssetEndpoint = $env:AUTOMATION_ASSET_ENDPOINT
        return $environment
    }

    # Check for Hybrid Runbook Worker
    if ($env:COMPUTERNAME -and (Get-Service -Name "Microsoft Monitoring Agent" -ErrorAction SilentlyContinue) -and 
        (Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\HybridRunbookWorker" -ErrorAction SilentlyContinue)) {
        $environment.Type = "Hybrid Runbook Worker"
        $environment.IsHybridWorker = $true
        $environment.Details.ComputerName = $env:COMPUTERNAME
        $environment.Details.UserDomain = $env:USERDOMAIN
        return $environment
    }

    # Check for PowerShell ISE
    if ($psISE) {
        $environment.Type = "PowerShell ISE"
        $environment.IsPowerShellISE = $true
        $environment.Details.Version = $psISE.PowerShellVersion
        $environment.Details.ComputerName = $env:COMPUTERNAME
        return $environment
    }

    # Check for VS Code
    if ($env:TERM_PROGRAM -eq "vscode" -or $env:VSCODE_PID) {
        $environment.Type = "Visual Studio Code"
        $environment.IsVSCode = $true
        $environment.Details.TermProgram = $env:TERM_PROGRAM
        $environment.Details.ComputerName = $env:COMPUTERNAME
        return $environment
    }

    # Check for Windows Terminal or PowerShell Console
    if ($env:WT_SESSION -or $env:WT_PROFILE_ID) {
        $environment.Type = "Windows Terminal"
        $environment.IsLocalTerminal = $true
        $environment.Details.WTSession = $env:WT_SESSION
        $environment.Details.ComputerName = $env:COMPUTERNAME
        return $environment
    }

    # Default to Local Terminal/Console
    $environment.Type = "Local PowerShell Console"
    $environment.IsLocalTerminal = $true
    $environment.Details.ComputerName = $env:COMPUTERNAME
    $environment.Details.UserName = $env:USERNAME
    $environment.Details.UserDomain = $env:USERDOMAIN
    $environment.Details.PSVersion = $PSVersionTable.PSVersion.ToString()
    
    return $environment
}

# Test the detection
$ExecutionEnvironment = Get-ExecutionEnvironment

Write-Host ""
Write-Host "DETECTED ENVIRONMENT:" -ForegroundColor Green
Write-Host "  Type: $($ExecutionEnvironment.Type)" -ForegroundColor White
Write-Host "  Azure Function: $($ExecutionEnvironment.IsAzureFunction)" -ForegroundColor Gray
Write-Host "  Azure Automation: $($ExecutionEnvironment.IsAzureAutomation)" -ForegroundColor Gray
Write-Host "  Hybrid Worker: $($ExecutionEnvironment.IsHybridWorker)" -ForegroundColor Gray
Write-Host "  PowerShell ISE: $($ExecutionEnvironment.IsPowerShellISE)" -ForegroundColor Gray
Write-Host "  VS Code: $($ExecutionEnvironment.IsVSCode)" -ForegroundColor Gray
Write-Host "  Local Terminal: $($ExecutionEnvironment.IsLocalTerminal)" -ForegroundColor Gray

Write-Host ""
Write-Host "ENVIRONMENT DETAILS:" -ForegroundColor Green
foreach ($detail in $ExecutionEnvironment.Details.GetEnumerator()) {
    if ($detail.Value) {
        Write-Host "  $($detail.Key): $($detail.Value)" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "SYSTEM INFORMATION:" -ForegroundColor Green
Write-Host "  Computer Name: $($env:COMPUTERNAME)" -ForegroundColor White
Write-Host "  User: $($env:USERDOMAIN)\$($env:USERNAME)" -ForegroundColor White
Write-Host "  PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
Write-Host "  PowerShell Edition: $($PSVersionTable.PSEdition)" -ForegroundColor White
Write-Host "  OS: $($PSVersionTable.OS)" -ForegroundColor White

Write-Host ""
Write-Host "RELEVANT ENVIRONMENT VARIABLES:" -ForegroundColor Green
$relevantVars = @(
    "FUNCTIONS_WORKER_RUNTIME",
    "AzureWebJobsStorage", 
    "WEBSITE_SITE_NAME",
    "AUTOMATION_ASSET_ENDPOINT",
    "AutomationAccountName",
    "TERM_PROGRAM",
    "VSCODE_PID",
    "WT_SESSION",
    "WT_PROFILE_ID"
)

foreach ($var in $relevantVars) {
    $value = [Environment]::GetEnvironmentVariable($var)
    if ($value) {
        Write-Host "  ${var}: $value" -ForegroundColor White
    } else {
        Write-Host "  ${var}: (not set)" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "SERVICES CHECK:" -ForegroundColor Green
$services = @("Microsoft Monitoring Agent", "HealthService")
foreach ($serviceName in $services) {
    $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
    if ($service) {
        Write-Host "  ${serviceName}: $($service.Status)" -ForegroundColor White
    } else {
        Write-Host "  ${serviceName}: Not Found" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "REGISTRY CHECK:" -ForegroundColor Green
$regPaths = @(
    "HKLM:\SOFTWARE\Microsoft\HybridRunbookWorker",
    "HKLM:\SOFTWARE\Microsoft\Microsoft Operations Manager"
)

foreach ($regPath in $regPaths) {
    $regKey = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
    if ($regKey) {
        Write-Host "  ${regPath}: Found" -ForegroundColor White
    } else {
        Write-Host "  ${regPath}: Not Found" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "LOGGING BEHAVIOR TEST:" -ForegroundColor Green
if ($ExecutionEnvironment.IsAzureFunction -or $ExecutionEnvironment.IsAzureAutomation) {
    Write-Host "  Logging Mode: Azure (Write-Output with UTC timestamps)" -ForegroundColor Yellow
    $utcTimestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
    Write-Output "$utcTimestamp INFO: This is how logs appear in Azure environments"
} else {
    Write-Host "  Logging Mode: Local (Write-Host with colors and local timestamps)" -ForegroundColor Yellow
    $timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$timestamp] This is how logs appear in local environments" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "ENVIRONMENT DETECTION TEST COMPLETED" -ForegroundColor Magenta
